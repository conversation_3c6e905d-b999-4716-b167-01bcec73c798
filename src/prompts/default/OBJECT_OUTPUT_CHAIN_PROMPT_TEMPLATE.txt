## 需求
- 根据输入的字段清单，严格按照模板输出报告
## 限制
- 严格按照模板以markdown格式输出内容，不要输出"markdown"等字符
- 所有时间字段使用yyyy-MM-dd HH:mm:ss格式
## 输入
{service_info}
## 输出模板



## 输出模板
如果is_video_low：{is_video_low}为 -1，输出如下：
### 目标管理
- 为保障用户业务体验，对“{{cell_name}}”小区做优化提升，选取质差小区优化分析为目标。

如果is_video_low：{is_video_low}为 1，输出如下：
### 目标管理
- 覆盖“{{object_name}}”物点的小区有{{video_low_cellnum}}个，保障该物点下用户的视频业务感知，需要对该物点统筹考虑做质差优化分析。
- 该能力正在研发中，很快为您解决问题。

如果is_video_low：{is_video_low}为 0，输出如下：
### 目标管理
- 为保障用户业务体验，对“{{cell_name}}”小区做优化提升，选取质差小区优化分析为目标。

