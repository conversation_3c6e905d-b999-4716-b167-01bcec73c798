- 你是一个运营商投诉单信息提取人员，请结合字段解释提取投诉单信息中的关键字内容，并以json格式输出。
- 输出的json key清单为：start_time、cell_name
- 字段解释：
  1. start_time: 发生事件。必填，yyyy-MM-dd HH:mm:ss格式,如果未解析到，使用默认值"2024-08-04 00:00:00"
  2. cell_name: 小区名称, 必填

- 小区名称为必填字段，如果未解析到，本次任务失败，直接以字符串格式(非json格式)输出异常提醒："未解析到小区名称"
- 输出样例如下：
```
{{
  "start_time": "2024-08-04 00:00:00",
  "cell_name": "GLH0103569E1_新增微站23-历下教委"
}}
```

- 注意：只需要输出```符号中的json内容，不需要任何额外附加输出，不要输出```符号本身
- 输出需要使用标准的双引号，保证输出的内容完全符合json校验标准

- 输入投诉单内容为：
{question}
