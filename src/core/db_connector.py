import configparser
import os
import psycopg2
import time

cf = configparser.ConfigParser()
# file = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
file = os.path.join(os.path.dirname(__file__), "../config/db_config.txt")
cf.read(file, encoding='utf-8')
HOST = cf.get('pgsql', 'host')
PORT = cf.getint('pgsql', 'port')
USER = cf.get('pgsql', 'user')
PASSWD = cf.get('pgsql', 'passwd')
DB_NAME = cf.get('pgsql', 'db_name')
ALARM_TABLE = cf.get('pgsql', 'alarm_table')
STATE_HISTORY_TABLE = cf.get('pgsql', 'state_history_table')
EVALUATE_HISTORY_TABLE = cf.get('pgsql', 'task_evaluate_history')
REPORT_TABLE = cf.get('pgsql', 'report_table')


class DbConn:
    def __init__(self):
        self.cursor = None
        self.host = HOST
        self.user = USER
        self.passwd = PASSWD
        self.db = DB_NAME
        self.port = PORT
        self.alarm_table = ALARM_TABLE
        self.state_history_table = STATE_HISTORY_TABLE
        self.evaluate_history_table = EVALUATE_HISTORY_TABLE
        self.report_table = REPORT_TABLE
        self.conn = None
        self._conn()

    def _conn(self):
        try:
            self.conn = psycopg2.connect(host=self.host, port=self.port, user=self.user, password=self.passwd,
                                         database=self.db)
            return True
        except:
            return False

    def _reconn(self, num=3, stime=3):
        _number = 0
        _status = True
        while _status and _number <= num:
            try:
                self.conn.ping()
                _status = False
            except:
                if self._conn():
                    _status = False
                    break
                _number += 1
                time.sleep(stime)

    def read_db_data(self, sql=''):
        self._reconn()
        self.cursor = self.conn.cursor()
        self.cursor.execute(sql)
        col_names = [i[0] for i in self.cursor.description]
        result = self.cursor.fetchall()
        self.conn.commit()
        self.cursor.close()
        return result, col_names

    def execute_sql_withdf(self, data, sql=''):
        self._reconn()
        self.cursor = self.conn.cursor()
        self.cursor.execute(sql, data)
        self.conn.commit()
        self.cursor.close()
        return True

    def executemany_sql_withdf(self, data, sql=''):
        self._reconn()
        self.cursor = self.conn.cursor()
        self.cursor.executemany(sql, data)
        self.conn.commit()
        self.cursor.close()
        return True

    def execute_sql_nodf(self, sql=''):
        self._reconn()
        self.cursor = self.conn.cursor()
        self.cursor.execute(sql)
        self.conn.commit()
        self.cursor.close()
        return True

    def close(self):
        self.conn.close()
