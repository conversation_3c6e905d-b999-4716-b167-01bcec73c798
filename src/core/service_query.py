import configparser
import os
from typing import List
from an_copilot.framework.logging import an_logger as logger
import psycopg2

cf = configparser.ConfigParser()
# file = os.path.dirname(os.path.abspath("."))
file = os.path.join(os.path.dirname(__file__), "../config/db_config.txt")
cf.read(file, encoding='utf-8')
host = cf.get('sg_pgsql', 'host')
port = cf.getint('sg_pgsql', 'port')
user = cf.get('sg_pgsql', 'user')
passwd = cf.get('sg_pgsql', 'passwd')
db_name = cf.get('sg_pgsql', 'db_name')
table_name = cf.get('sg_pgsql', 'seamless_table')
cell_log = cf.get('sg_pgsql', 'cell_log')
cluster_object = cf.get('sg_pgsql', 'cluster_object')
xdr_video = cf.get('sg_pgsql', 'xdr_video')


class SeamlessDB:
    def __init__(self):
        self.conn_params = f"host='{host}' port='{port}' user='{user}' password='{passwd}' database='{db_name}' "
        self.conn = None
        self.cursor = None
        self.connect()

    def connect(self):
        self.conn = psycopg2.connect(host=host, port=port, user=user, password=passwd, database=db_name)
        self.cursor = self.conn.cursor()

    def close(self):
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()

    def get_video_low_cellnum(self,cell_name,start_time):
        # 第一步：根据小区名称查询物点ID
        query = f"""SELECT DISTINCT  object_name FROM {table_name}   WHERE cell_name = %s
                """
        try:
            result_dict ={}
            logger.info(f"query_video_low_cellnum sql: {query},cell_name: {cell_name}")
            self.cursor.execute(query, (cell_name,))
            object_id_result = self.cursor.fetchone()
            if object_id_result:
                object_name = object_id_result[0]
                query_all_cells = f"""SELECT  is_video_low,video_low_cellnum  FROM   {cluster_object}
                            WHERE  object_name = %s and start_time  = %s
                        """
                logger.info(f"query_video_low_cellnum sql: {query_all_cells},object_name: {object_name},start_time：{start_time} ")
                self.cursor.execute(query_all_cells, (object_name, start_time))
                cellnum_result = self.cursor.fetchone()
                if cellnum_result:
                    is_video_low = 1 if cellnum_result[0] else 0
                    cellnum = cellnum_result[1]
                    result_dict = {
                        'object_name': object_name,
                        'is_video_low': is_video_low,
                        'video_low_cellnum': cellnum
                    }
            return result_dict
        except psycopg2.Error as ex:
            logger.error(f"查询失败: {ex}"  )
            return {}

    def insert_cell_log(self, start_time, cell_name, btree_code,btree_context,plan_info):
        insert_query = f"""
                    INSERT INTO {cell_log} (start_time, cell_name, btree_code,btree_context,plan_info,create_time) 
                    VALUES (%s, %s, %s,%s, %s, CURRENT_TIMESTAMP) 
                """
        try:
            logger.info(f"执行的SQL插入: {insert_query} ")
            self.cursor.execute(insert_query, (start_time, cell_name, btree_code,btree_context,plan_info))
            self.conn.commit()
            return True
        except psycopg2.Error as ex:
            logger.error(f"插入失败:{ex}")
            return False

    def get_cell_log(self, start_time,cell_name):
        query = f"""
                SELECT plan_info  FROM {cell_log} WHERE start_time = %s and  cell_name = %s  
                """
        try:
            logger.info(f"get_cell_log 查询sql: {query}", )
            self.cursor.execute(query, (start_time,cell_name))
            result = self.cursor.fetchone()
            if result:
                columns = [desc[0] for desc in self.cursor.description]
                result_dict = dict(zip(columns, result))
            else:
                logger.info(f"未查询到与 cell_name={cell_name} 对应的数据")
                result_dict = {}
            return result_dict
        except psycopg2.Error as ex:
            logger.error(f"查询失败: {ex}")
            return ""


    def get_cell_time(self,cell_name):
        query = f"""
                SELECT start_time  FROM {xdr_video} WHERE   cell_name = %s  order by start_time desc limit 1
                """
        start_time = '2024-08-04 00:00:00'
        try:
            logger.info(f"get_cell_time 查询sql: {query}", )
            self.cursor.execute(query, (cell_name,))
            result = self.cursor.fetchone()
            if result:
                start_time = result[0].strftime("%Y-%m-%d %H:%M:%S")
            else:
                logger.info(f"未查询到与 cell_name={cell_name} 对应的数据")
            return start_time
        except psycopg2.Error as ex:
            logger.error(f"查询失败: {ex}")
            return start_time

def query_video_low_cellnum(cell_name,start_time):
    try:
        db = SeamlessDB()
        data = db.get_video_low_cellnum(cell_name,start_time)
        logger.info("query_video_low_cellnum success ")
        logger.info(f"query_video_low_cellnum : {data}", )
        db.close()
        return data
    except Exception as e:
        logger.error(f"query_seamless_objects failed: {e}")
        return None

def save_cell_log(start_time, cell_name, btree_code,btree_context,plan_info):
    db = SeamlessDB()
    try:
        db.insert_cell_log(start_time, cell_name, btree_code,btree_context,plan_info)
    except Exception as e:
        logger.error("insert failed: %r", e)
    db.close()


def query_cell_log(start_time,cell_name):
    try:
        db = SeamlessDB()
        data = db.get_cell_log(start_time,cell_name)
        logger.info("query_cell_log success ")
        logger.info(f"query_cell_log : {data}", )
        db.close()
        return data
    except Exception as e:
        logger.error(f"query_cell_log failed: {e}")
        return None

def query_cell_time(cell_name):
    try:
        db = SeamlessDB()
        data = db.get_cell_time(cell_name)
        logger.info("query_cell_time success ")
        logger.info(f"query_cell_time : {data}", )
        db.close()
        return data
    except Exception as e:
        logger.error(f"query_cell_time failed: {e}")
        return None

if __name__ == '__main__':
    print(query_cell_time('GGH01A6512R1_万科麓山-草山岭' ))
