import os
from typing import Dict

from an_copilot.framework.config.common_settings import CommonConfig, CommonSettings
from pydantic import BaseModel


def prepare_environment(config_json: Dict):
    env_to_action_map = {
        "WIRELESS_GUARANTEE_COPILOT_GPT_BTREE_CODE_GENERATING": "generate_scheme",
    }
    if "wg_agent_config" not in config_json:
        config_json["wg_agent_config"] = {}
    if "btree_codes" not in config_json["wg_agent_config"]:
        config_json["wg_agent_config"]["btree_codes"] = {}

    for env_var, action_name in env_to_action_map.items():
        if os.environ.get(env_var):
            config_json["wg_agent_config"]["btree_codes"][action_name] = os.environ.get(env_var)

    if "service_btree_code" not in config_json["service_agent_config"]:
        config_json["service_agent_config"]["service_btree_code"] = ""

    if os.environ.get("SERVICE_BTREE_CODE"):
        config_json["service_agent_config"]["service_btree_code"] = os.environ.get(
            "SERVICE_BTREE_CODE"
        )


class WGAgentConfig(BaseModel):
    btree_codes: Dict[str, str]
    an_oauth_base_url: str
    an_copilot_knowledge_base_url: str

class ServiceAgentConfig(BaseModel):
    service_btree_code: str

class Config(CommonConfig):
    wg_agent_config: WGAgentConfig
    service_agent_config: ServiceAgentConfig


class Settings(CommonSettings):
    def __init__(self):
        super().__init__(
            custom_config_path=os.path.join(os.path.dirname(__file__), "config.json"),
            custom_prepare_environment=prepare_environment,
            custom_config_cls=Config,
            prompts_path=os.path.join(os.path.dirname(__file__), "../prompts"),
        )
