[pgsql]
host = ***********
port = 54321
user = postgres
passwd = retina
db_name = agent_db
alarm_table = wireless_guarantee.tm_alarm_task_info
state_history_table = wireless_guarantee.tm_task_state_history
task_evaluate_history = wireless_guarantee.tm_task_evaluate_history
report_table = wireless_guarantee.tm_alarm_report

[sg_pgsql]
host = ***********
port = 5432
user = postgres
passwd = retina
db_name = intelligence
seamless_table = public.dim_seamless_object_sd
cell_log = service_guarantee.tm_task_history
xdr_video =  public.to_xdr_video_cell_h
cluster_object =  public.tm_cluster_object_h
