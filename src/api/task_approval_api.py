import json
import uuid
from datetime import datetime
from typing import Optional

from an_copilot.framework.logging import an_logger
from fastapi import APIRouter, Body, HTTPException
from pydantic import BaseModel, Field

from src.agents.wireless_guarantee_agent.wg_agent import WGAgent
from src.core.db_connector import DbConn

router = APIRouter()


class TaskApprovalRequest(BaseModel):
    """任务审批请求模型"""
    serialno: str = Field(..., description="告警流水号")
    scheme_id: str = Field(..., description="方案ID")
    approval_user: str = Field(..., description="审批用户")
    approval_time: str = Field(..., description="审批时间，格式：YYYY-MM-DD HH:mm:ss")
    approval_result: bool = Field(..., description="审批结果，true表示通过，false表示拒绝")


class TaskApprovalResponse(BaseModel):
    """任务审批响应模型"""
    success: bool = Field(..., description="处理是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[dict] = Field(None, description="响应数据")


@router.post(
    "/api/task/approve",
    status_code=200,
    summary="任务审批接口",
    response_model=TaskApprovalResponse
)
async def approve_task(
    req: TaskApprovalRequest = Body(...),
) -> TaskApprovalResponse:
    """
    任务审批接口

    接收外部系统的审批结果，根据流水号查询基础信息并初始化WGAgent实例进行处理
    """
    an_logger.info(f"收到任务审批请求: {req}")

    try:
        # 1. 参数验证
        if not req.serialno:
            raise HTTPException(status_code=400, detail="流水号不能为空")

        # 验证审批时间格式
        try:
            datetime.strptime(req.approval_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(status_code=400, detail="审批时间格式错误，应为：YYYY-MM-DD HH:mm:ss")

        # 2. 根据流水号查询tm_alarm_task_info表记录
        db_conn = None
        try:
            db_conn = DbConn()
            query_sql = f"""
                SELECT session_id
                FROM {db_conn.alarm_table}
                WHERE serialno = %s
            """
            result, col_names = db_conn.read_db_data(query_sql % f"'{req.serialno}'")

            if not result or len(result) == 0:
                an_logger.warning(f"根据流水号 {req.serialno} 未查询到记录")
                return TaskApprovalResponse(
                    success=False,
                    message=f"根据流水号 {req.serialno} 未查询到记录"
                )

            # 直接获取session_id字段
            session_id = result[0][0] if result[0][0] else None

            if not session_id:
                an_logger.warning(f"从记录中未获取到有效的 session_id")
                return TaskApprovalResponse(
                    success=False,
                    message=f"从记录中未获取到有效的 session_id"
                )

            an_logger.info(f"查询到记录，session_id: {session_id}")

        except Exception as e:
            an_logger.error(f"查询数据库记录失败: {e}")
            return TaskApprovalResponse(
                success=False,
                message=f"查询数据库记录失败: {str(e)}"
            )
        finally:
            if db_conn:
                db_conn.close()

        # 3. 初始化WGAgent实例
        try:
            wg_agent = WGAgent(session_id=session_id)
            an_logger.info(f"WGAgent实例初始化成功，session_id: {session_id}")
        except Exception as e:
            an_logger.error(f"初始化WGAgent实例失败: {e}")
            return TaskApprovalResponse(
                success=False,
                message=f"初始化WGAgent实例失败: {str(e)}"
            )

        # 4. 准备请求参数并调用_run接口
        try:
            request_id = uuid.uuid4().hex  # 生成32位十六进制字符串，格式如：f6dacef18b8f407faf00de492dabd786

            # 构造question参数（使用请求入参）
            question = json.dumps({
                "serialno": req.serialno,
                "scheme_id": req.scheme_id,
                "approval_user": req.approval_user,
                "approval_time": req.approval_time,
                "approval_result": req.approval_result
            })

            # 调用_run接口
            wg_agent.run(
                request_id=request_id,
                question=question
            )

            an_logger.info(f"智能体调用成功，request_id: {request_id}")

            # 5. 返回成功响应
            response_message = f"审批结果已处理：{'通过' if req.approval_result else '拒绝'}"
            an_logger.info(response_message)

            return TaskApprovalResponse(
                success=True,
                message=response_message,
                data={
                    "serialno": req.serialno,
                    "approval_result": req.approval_result
                }
            )

        except Exception as e:
            an_logger.error(f"智能体调用失败: {e}")
            return TaskApprovalResponse(
                success=False,
                message=f"智能体调用失败: {str(e)}"
            )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        an_logger.error(f"处理任务审批请求时发生异常: {e}")
        return TaskApprovalResponse(
            success=False,
            message=f"处理审批请求时发生异常: {str(e)}"
        )
