import json
import time

from an_contract.framework.agent_entity import AdditionalType, AgentResponseAdditional
from an_copilot.framework.logging import an_logger
from an_copilot.framework.utils.btree import (
    BTree,
    BtreeContext,
    BtreeRegistry,
    behaviour,
    BehaviorResponse,
    Status,
)
from an_copilot.framework.utils.httpclient import HttpClient

from src.core.ces_stream_sender import CesStreamSender


class GoalRunner:
    btree_code: str
    http_client: HttpClient = None
    context: BtreeContext = None
    ces_stream_sender: CesStreamSender = None
    
    # 常量定义
    WEAK_GRID_RATE_THRESHOLD = 0.3
    EVALUATING_STATES = {"evaluating", "resetting"}
    
    # 状态映射
    STATUS_MAPPING = {
        "SUCCESS": True,
        "FAILURE": False
    }

    def __init__(self, btree_code: str):
        self.btree_code = btree_code
        # 延迟导入避免循环导入
        from src.config import settings
        self.http_client = HttpClient(base_url=settings.config.ces.uri)
        self.ces_stream_sender = CesStreamSender(settings.ces_stream)

    def run(self, inputs: dict = None):
        begin_time = time.time()
        session_id = inputs.get("session_id")
        request_id = inputs.get("request_id")
        
        # 获取并发送行为树
        btree_dict = self.get_goal_retrieval(name=self.btree_code)
        btree_json = btree_dict["data"]
        self._send_btree_start(session_id, request_id, begin_time, btree_json)
        
        # 初始化上下文
        self._initialize_context(inputs)
        
        # 执行行为树
        response = self._execute_btree(btree_json)
        
        # 处理执行结果
        node_result_dict, behaviour_function = self._process_btree_response(response)
        
        # 发送结束消息
        self._send_btree_end(session_id, request_id, begin_time, behaviour_function)
        
        # 更新上下文
        self._update_context_results(node_result_dict)
    
    def _send_btree_start(self, session_id: str, request_id: str, begin_time: float, btree_json: dict):
        """发送行为树开始消息"""
        btree_additional = build_btree_additional(btree=btree_json)
        self.ces_stream_sender.begin(session_id, request_id, begin_time)
        self.ces_stream_sender.send_start(session_id, request_id, begin_time)
        self.ces_stream_sender.send_attachment(session_id, request_id, btree_additional, begin_time)
    
    def _initialize_context(self, inputs: dict):
        """初始化行为树上下文"""
        self.context = BtreeContext()
        if inputs:
            an_logger.info(f"inputs: {inputs}")
            for k, v in inputs.items():
                self.context.put(k, v)
    
    def _execute_btree(self, btree_json: dict):
        """执行行为树"""
        btree = BTree.from_config(
            btree_json,
            BtreeRegistry.get_nodes(),
            context=self.context,
        )
        an_logger.info(f"running btree: {self.btree_code}")
        return btree.run(show_status=True)
    
    def _process_btree_response(self, response):
        """处理行为树执行响应"""
        node_result_dict = {}
        behaviour_function = {}
        
        for node in response.get_nodes():
            node_info = self._extract_node_info(node)
            behaviour_function[node.name] = node_info
            an_logger.info(f"NODE: --> {node_info}")
            
            # 仅记录action节点的执行结果
            if node.type == "action":
                run_result = self.STATUS_MAPPING.get(node.status.name)
                node_result_dict[node.name] = run_result
        
        return node_result_dict, behaviour_function
    
    def _extract_node_info(self, node) -> dict:
        """提取节点信息"""
        return {
            "type": node.type,
            "name": node.name,
            "message": node.message,
            "duration": node.duration,
            "status": node.status.name,
            "title": node.title,
        }
    
    def _send_btree_end(self, session_id: str, request_id: str, begin_time: float, behaviour_function: dict):
        """发送行为树结束消息"""
        btree_function_additional = build_btree_function_additional(behaviour_function)
        self.ces_stream_sender.send_attachment(session_id, request_id, btree_function_additional, begin_time)
        self.ces_stream_sender.send_end(session_id, request_id, begin_time)
        self.ces_stream_sender.end(session_id, request_id, begin_time)
    
    def _update_context_results(self, node_result_dict: dict):
        """更新上下文中的节点结果"""
        existing_node_results = self.context.get("node_results") or {}
        existing_node_results.update(node_result_dict)
        self.context.put("node_results", existing_node_results)
        an_logger.info(f"context data: {self.context.get_data()}")


    def get_goal_retrieval(self, name: str) -> dict:
        """获取行为树的json"""
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "from": "Y",
        }
        
        endpoint = f"/workflow/getNodesJson?code={name}&version=1.0.0"
        try:
            response = self.http_client.get(endpoint=endpoint, headers=headers)
            response_json = response.json()
            response_json["data"] = json.loads(response_json["data"])
            return response_json
        except Exception as e:
            an_logger.error(f"获取行为树配置失败: {e}")
            raise

    @behaviour
    def weak_rate_no_effect(self: BtreeContext) -> BehaviorResponse:
        weak_grid_rate = self.get("weak_grid_rate")
        status = Status.SUCCESS if weak_grid_rate >= GoalRunner.WEAK_GRID_RATE_THRESHOLD else Status.FAILURE
        return BehaviorResponse(status=status)

    @behaviour
    def is_evaluating_or_resetting(self: BtreeContext) -> BehaviorResponse:
        current_state = self.get("current_state")
        if not current_state:
            an_logger.error("current state is None")
            return BehaviorResponse(status=Status.FAILURE)
        
        status = Status.SUCCESS if current_state in GoalRunner.EVALUATING_STATES else Status.FAILURE
        return BehaviorResponse(status=status)


def build_btree_additional(btree: dict) -> AgentResponseAdditional:
    """构建行为树附加数据"""
    return _build_additional("behaviour_tree", btree)


def build_btree_function_additional(behaviour_function: dict) -> AgentResponseAdditional:
    """构建行为树函数附加数据"""
    return _build_additional("behaviour_function", behaviour_function)


def _build_additional(data_type: str, value: dict) -> AgentResponseAdditional:
    """构建附加数据的通用方法"""
    return AgentResponseAdditional(
        type=AdditionalType.JSON_DATA,
        value=json.dumps(
            {
                "type": data_type,
                "value": value,
            },
            ensure_ascii=False,
        ),
    )

