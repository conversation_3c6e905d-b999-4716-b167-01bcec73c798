import json
from typing import ClassV<PERSON>, Dict, List, Optional

from an_contract.framework.agent_entity import AgentR<PERSON>ponse
from an_copilot.framework.agent.copilot_agent import Copilot<PERSON>gent, HistoryMode
from an_copilot.framework.chain.copilot_chain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from an_copilot.framework.logging import an_logger
from langchain.callbacks.manager import Callbacks
from langchain.prompts import PromptTemplate

from src.agents.wireless_guarantee_agent.reflection.info_extraction_chain import InfoEx<PERSON><PERSON>hain
from src.agents.wireless_guarantee_agent.reflection.intent_recognition_chain import IntentR<PERSON>ognition<PERSON>hain
from src.agents.wireless_guarantee_agent.reflection.before_report_chain import BeforeReport<PERSON>hain
from src.agents.wireless_guarantee_agent.reflection.output_report_chain import OutputReport<PERSON>hain
from src.agents.wireless_guarantee_agent.reflection.measure_detail_chain import MeasureDetailChain
from src.agents.wireless_guarantee_agent.reflection.state_machine import WGStateMachine
from src.agents.wireless_guarantee_agent.reflection.state_router import StateMachineFactory
from src.core.ces_stream_sender import CesStreamSender


class WGAgent(CopilotAgent):
    name: str = "wg_agent"
    description: str = "无线保障处理智能体"
    history_mode = HistoryMode.ALL.value
    skills: ClassVar[List[str]] = ["start"]
    state_machine: WGStateMachine = None
    prepare_report_chain: BeforeReportChain = None
    alarm_report_chain: OutputReportChain = None
    schema_detail_chain: MeasureDetailChain = None

    def __init__(self, session_id: str):
        # 延迟导入避免循环导入
        from src.config import settings
        super().__init__(
            session_id=session_id,
            callbacks=settings.get_tracing_factory().getTracing(),
        )
        self.ces_stream_sender = CesStreamSender(settings.ces_stream)
        self.intent_recognition_chain = IntentRecognitionChain.from_settings(session_id=self.session_id, settings=settings)
        self.info_extraction_chain = InfoExtractionChain.from_settings(session_id=self.session_id, settings=settings)
        self.prepare_report_chain = BeforeReportChain.from_settings(session_id=self.session_id, settings=settings)
        self.factory = StateMachineFactory(WGStateMachine)
        self.alarm_report_chain = OutputReportChain.from_settings(session_id=self.session_id, settings=settings)
        self.schema_detail_chain = MeasureDetailChain.from_settings(session_id=self.session_id, settings=settings)
        

    def _run(
        self,
        request_id: str,
        question: str,
        tags: Optional[List[str]],
        user_agent_config: Optional[dict] = None,
        history: List[List[str]] = None,
        previous_agent_history: List[Dict[str, List[List[str]]]] = None,
        history_messages: Optional[List[Dict]] = None,
        previous_agent_history_messages: Optional[List[Dict[str, List[Dict]]]] = None,
        agent_contexts: List[str] = [],
        contexts: Optional[dict] = None,
        prompt_template: PromptTemplate = None,
        callbacks: Callbacks = None,
        reasoning: Optional[bool] = False,
    ) -> AgentResponse:
        # 意图识别
        intent_recognition = self.intent_recognition_chain.run(
            CopilotChain.chain_input_wrapper(question=question, history=None),
            tags=tags, callbacks=callbacks
        )
        # 信息提取
        info_extraction = self.info_extraction_chain.run(
            CopilotChain.chain_input_wrapper(question=intent_recognition.value, history=None),
            tags=tags, callbacks=callbacks
        )
        info = json.loads(info_extraction.value)
        serialno = info.get("serialno")
        self.state_machine = self.factory.get_or_create(sm_id=serialno)
        sm_context = info.get("extract_info")
        sm_context["session_id"] = self.session_id
        an_logger.info(f"session_id: {self.session_id}")
        sm_context["request_id"] = request_id
        an_logger.info(f"request_id: {request_id}")
        intention_type = info.get("recognition", "1")
        sm_context["intention_type"] = intention_type
        # 前4个阶段输出
        self.prepare_report_chain.run(
            CopilotChain.chain_input_wrapper(question=sm_context, history=None),
            tags=tags,
            callbacks=callbacks,
        )
        # 状态机运行
        self.state_machine.start(inputs=sm_context)
        # 任务执行阶段输出
        self.alarm_report_chain.run(
            CopilotChain.chain_input_wrapper(question=self.state_machine.sm_context, history=None),
            tags=tags,
            callbacks=callbacks,
        )
        # 方案详情阶段输出
        self.schema_detail_chain.run(
            CopilotChain.chain_input_wrapper(question=self.state_machine.sm_context, history=None),
            tags=tags,
            callbacks=callbacks,
        )
        
        return AgentResponse(
            agent_name=self.name,
            agent_input=question,
            agent_output="",
        )


WGAgent.register()
