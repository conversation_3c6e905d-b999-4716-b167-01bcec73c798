import json
from typing import Any, Dict, List, Optional, ClassVar
from an_copilot.framework.chain.copilot_chain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LLmGeneration
from an_copilot.framework.logging import an_logger
from langchain_core.callbacks import CallbackManagerFor<PERSON>hainRun
from src.core.db_connector import Db<PERSON>onn, ALARM_TABLE, EVALUATE_HISTORY_TABLE


class InfoExtractionChain(CopilotChain):
    """根据意图，提取不同的信息"""

    # 意图类型常量
    INTENT_ACTIVE_WORKFLOW: ClassVar[str] = "1"
    INTENT_APPROVAL_PROCESS: ClassVar[str] = "2"
    INTENT_EXECUTE_PROCESS: ClassVar[str] = "3"
    INTENT_EVALUATE_PROCESS: ClassVar[str] = "4"
    INTENT_CLEAR_WORKFLOW: ClassVar[str] = "5"

    # 数据库操作配置
    DB_OPERATIONS: ClassVar[Dict[str, Any]] = {
        INTENT_ACTIVE_WORKFLOW: {
            'type': 'insert',
            'table': ALARM_TABLE,
            'keys': [
                'serialno', 'sm_id', 'site_name', 'province_id', 'city_id', 'province', 'city',
                'lon', 'lat', 'occur_time', 'network_type', 'alarm_title',
                'std_severity', 'cover_scene', 'scheme_id', 'session_id',
                'approval_user', 'approval_time', 'approval_result', 'execute_time', 'execute_result'
            ],
            'extra_fields': {'sm_id': 'serialno', 'session_id': 'session_id'}
        },
        INTENT_APPROVAL_PROCESS: {
            'type': 'update',
            'table': ALARM_TABLE,
            'keys': ['scheme_id', 'approval_user', 'approval_time', 'approval_result']
        },
        INTENT_EXECUTE_PROCESS: {
            'type': 'update',
            'table': ALARM_TABLE,
            'keys': ['scheme_id', 'execute_time', 'execute_result']
        },
        INTENT_EVALUATE_PROCESS: {
            'type': 'insert',
            'table': EVALUATE_HISTORY_TABLE,
            'keys': [
                'serialno', 'scheme_id', 'evaluate_time', 'evaluate_round', 'evaluate_result',
                'evaluate_indicators', 'evaluate_period_type', 'evaluate_start_time', 'evaluate_end_time'
            ]
        },
        INTENT_CLEAR_WORKFLOW: {
            'type': 'update',
            'table': ALARM_TABLE,
            'keys': ['clear_time'],
            'condition': lambda info: info.get("clear_time")
        }
    }

    def get_name(self) -> str:
        return "信息提取"

    def description(self) -> str:
        return "根据意图，提取不同的信息"

    @property
    def input_keys(self) -> List[str]:
        return ["input"]

    @property
    def output_keys(self) -> List[str]:
        return [self.output_key]

    def _call(self, inputs: Dict[str, Any], run_manager: Optional[CallbackManagerForChainRun] = None) -> Dict[str, str] | None:
        info = json.loads(inputs.get("input"), strict=False)
        input_type = info.get("input_type")
        recognition = info.get("recognition")
        alarm_info = info.get("alarm_info")
        serialno = alarm_info.get("serialno")
        
        # 执行数据库操作
        self._execute_database_operation(recognition, alarm_info, serialno)
        
        # 添加输入类型
        if input_type:
            alarm_info["input_type"] = input_type
        
        # 构建响应
        response = json.dumps({
            "serialno": serialno,
            "recognition": recognition,
            "extract_info": alarm_info
        }, ensure_ascii=False)
        
        an_logger.info(f"信息提取结果：{response}")
        return self.response_wrapper(LLmGeneration(content=response))
    
    def _execute_database_operation(self, recognition: str, alarm_info: Dict[str, Any], serialno: str):
        """执行数据库操作"""
        operation_config = self.DB_OPERATIONS.get(recognition)
        if not operation_config:
            return
        
        # 检查条件（如果有）
        if 'condition' in operation_config and not operation_config['condition'](alarm_info):
            return
        
        db = None
        try:
            db = DbConn()
            
            if operation_config['type'] == 'insert':
                self._execute_insert(db, operation_config, alarm_info, serialno)
            elif operation_config['type'] == 'update':
                self._execute_update(db, operation_config, alarm_info, serialno)
                
        except Exception as e:
            an_logger.error(f"信息提取入库失败：{e}")
        finally:
            if db:
                db.close()
    
    def _execute_insert(self, db: DbConn, config: Dict, alarm_info: Dict[str, Any], serialno: str):
        """执行INSERT操作"""
        insert_info = self.subdict(alarm_info, config['keys'])
        
        # 处理额外字段
        if 'extra_fields' in config:
            for field, source in config['extra_fields'].items():
                if source == 'serialno':
                    insert_info[field] = serialno
                elif source == 'session_id':
                    insert_info[field] = self.session_id
        
        if insert_info:  # 只有在有数据时才执行
            columns = ', '.join(insert_info.keys())
            placeholders = ', '.join(['%s'] * len(insert_info))
            values = tuple(insert_info.values())
            sql = f"INSERT INTO {config['table']} ({columns}) VALUES ({placeholders})"
            db.execute_sql_withdf(values, sql)
    
    def _execute_update(self, db: DbConn, config: Dict, alarm_info: Dict[str, Any], serialno: str):
        """执行UPDATE操作"""
        update_info = self.subdict(alarm_info, config['keys'])
        
        if update_info:  # 只有在有数据时才执行
            set_clauses = [f"{key}=%s" for key in update_info.keys()]
            set_clause = ', '.join(set_clauses)
            values = tuple(update_info.values())
            sql = f"UPDATE {config['table']} SET {set_clause} WHERE serialno='{serialno}'"
            db.execute_sql_withdf(values, sql)

    def subdict(self, d, keys):
        return {k: d[k] for k in keys if k in d}
