from typing import Any, Dict, List, Optional, ClassVar

from an_copilot.framework.chain.copilot_chain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LLmGeneration
from an_copilot.framework.logging import an_logger
from langchain.prompts import PromptTemplate
from langchain_core.callbacks import CallbackManagerForChainRun

from src.core.ces_stream_sender import CesStreamSender
from src.utils.knowledge_util import similarity_search, generate_content, save_report_to_db
import json
import time
class MeasureDetailChain(CopilotChain):
    ces_stream_sender: CesStreamSender = None

    # 意图类型常量
    ACTIVE_INTENTS: ClassVar[set] = {"1", "5"}
    # 执行阶段
    MESSAGE_STATE:ClassVar[str] = "方案详情"
    # 知识库配置
    PUSH_REPOSITORY: ClassVar[list] = ["无线保障消息推送库"] # 无线保障消息推送库


    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        # 延迟导入避免循环导入
        from src.config import settings
        self.ces_stream_sender = CesStreamSender(settings.ces_stream)

    def get_name(self) -> str:
        return "Measure Detail Report Chain"

    def description(self) -> str:
        return "生成方案详情报告"

    @property
    def input_keys(self) -> list[str]:
        return ["input"]

    @property
    def output_keys(self) -> list[str]:
        return [self.output_key]
    def _call(self, inputs: Dict[str, Any], run_manager: Optional[CallbackManagerForChainRun] = None)-> Any | None:
        try:
            begin_time = time.time()
            sm_context = inputs.get("input")
            intention_type = sm_context.get("intention_type") # 根据输入 可直接获得意图分类识别的结果
            session_and_request_json = json.loads(run_manager.tags[0])
            request_id = session_and_request_json.get("request_id")

            # 意图 1 或 5 发送推送消息
            if intention_type in self.ACTIVE_INTENTS:
                query = "意图" + intention_type + self.MESSAGE_STATE
                # 获取输出内容
                report_content = generate_content(self, query, self.PUSH_REPOSITORY, inputs, run_manager)
                # 存库
                save_report_to_db(sm_context, intention_type, report_content)
                an_logger.info("报告已保存")
                # 页面推送
                self.ces_stream_sender.send_msg(self.session_id, request_id, "\n" + report_content, begin_time)

            return self.response_wrapper(LLmGeneration(content = ""))

        except Exception as e:
            an_logger.error(f"SchemaDetailChain error: {e}")
            raise e
    
