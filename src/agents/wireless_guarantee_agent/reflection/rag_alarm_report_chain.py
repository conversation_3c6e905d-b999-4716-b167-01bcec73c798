from typing import Any, Dict, Optional, ClassVar

from an_copilot.framework.chain.copilot_chain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LLmGeneration
from an_copilot.framework.logging import an_logger
from langchain.prompts import PromptTemplate
from langchain_core.callbacks import Callback<PERSON>anager<PERSON><PERSON><PERSON>hainRun

from src.core.ces_stream_sender import CesStreamSender
from src.core.db_connector import DbConn
from src.utils.knowledge_util import similarity_search
import json
import time
from datetime import datetime


class RagAlarmReportChain(CopilotChain):
    ces_stream_sender: CesStreamSender = None

    # 意图类型常量
    ACTIVE_INTENTS: ClassVar[set] = {"1", "5"}
    ACTIVE_PHASE_INTENTS: ClassVar[set] = {"1", "2", "3", "4"}
    CLEARED_PHASE_INTENTS: ClassVar[set] = {"5", "6"}

    # 推送消息配置
    PUSH_MESSAGE_QUERIES: ClassVar[list] = [
        "任务执行",
        "软参优化补偿方案总结"
    ]

    # 知识库配置
    PUSH_REPOSITORY: ClassVar[list] = ["无线保障消息推送库"]
    KNOWLEDGE_REPOSITORY: ClassVar[list] = ["无线保障知识库"]

    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        # 延迟导入避免循环导入
        from src.config import settings
        self.ces_stream_sender = CesStreamSender(settings.ces_stream)

    def get_name(self) -> str:
        return "Rag Alarm Report Chain"

    def description(self) -> str:
        return "生成告警报告"

    @property
    def input_keys(self) -> list[str]:
        return ["input"]

    @property
    def output_keys(self) -> list[str]:
        return [self.output_key]

    def _call(self, inputs: Dict[str, Any], run_manager: Optional[CallbackManagerForChainRun] = None) -> Any | None:
        try:
            begin_time = time.time()
            sm_context = inputs.get("input")
            intention_type = sm_context.get("intention_type")
            session_and_request_json = json.loads(run_manager.tags[0])
            request_id = session_and_request_json.get("request_id")

            # 发送推送消息（如果需要）
            if intention_type in self.ACTIVE_INTENTS:
                self._send_push_messages(inputs, run_manager, request_id, begin_time)

            # 生成主报告
            response = self._generate_main_report(inputs, run_manager, intention_type)
            
            # 保存报告到数据库
            self._save_report_to_db(sm_context, intention_type, response.content)
            
            an_logger.info("报告已保存")
            return self.response_wrapper(llm_generation=response)
            
        except Exception as e:
            an_logger.error(f"AlarmReportChain error: {e}")
            raise e
    
    def _send_push_messages(self, inputs: Dict[str, Any], run_manager, request_id: str, begin_time: float):
        """发送推送消息"""
        for query in self.PUSH_MESSAGE_QUERIES:
            content = self._generate_content_from_knowledge(query, self.PUSH_REPOSITORY, inputs, run_manager)
            self.ces_stream_sender.send_msg(self.session_id, request_id, "\n" + content, begin_time)
    
    def _generate_main_report(self, inputs: Dict[str, Any], run_manager, intention_type: str) -> LLmGeneration:
        """生成主报告"""
        an_logger.info("开始生成报告...")
        
        search_query = "意图" + intention_type
        reference_documents_array = self._search_knowledge_base(search_query, self.KNOWLEDGE_REPOSITORY)
        
        # 处理返回的文档格式
        reference_documents_array = reference_documents_array[0].splitlines()[1:]
        reference_documents = "\n".join(reference_documents_array)
        
        prompt = PromptTemplate.from_template(reference_documents)
        an_logger.info(f"获取到prompt: \n{prompt.template}")

        prompt_value = prompt.format_prompt(**inputs)
        response = self.llm_generate(
            messages=[prompt_value.to_messages()], run_manager=run_manager
        )
        
        an_logger.info("报告已生成")
        return response
    
    def _generate_content_from_knowledge(self, query: str, repository: list, inputs: Dict[str, Any], run_manager) -> str:
        """从知识库生成内容"""
        reference_documents_array = self._search_knowledge_base(query, repository)
        reference_documents = reference_documents_array[0]
        
        prompt = PromptTemplate.from_template(reference_documents)
        prompt_value = prompt.format_prompt(**inputs)
        response = self.llm_generate(
            messages=[prompt_value.to_messages()], run_manager=run_manager
        )
        return response.content
    
    def _search_knowledge_base(self, query: str, repository: list) -> list:
        """搜索知识库的通用方法"""
        an_logger.info(f"[RagAlarmReportChain] 调用similarity_search: 查询='{query}', repository_alias={repository}")
        reference_documents_array = similarity_search(query, repository_alias=repository)
        an_logger.info(f"[RagAlarmReportChain] similarity_search返回结果数量: {len(reference_documents_array)}")
        
        if not reference_documents_array:
            an_logger.error(f"[RagAlarmReportChain] similarity_search返回空数组，查询='{query}'")
            raise ValueError(f"知识库查询'{query}'返回空结果")
            
        return reference_documents_array
    
    def _save_report_to_db(self, sm_context: Dict[str, Any], intention_type: str, report_content: str):
        """保存报告到数据库"""
        serialno = sm_context.get("serialno")
        phase = self._determine_phase(intention_type)
        
        if not serialno or not phase:
            return
            
        db_con = None
        try:
            db_con = DbConn()
            insert_sql = f"INSERT INTO {db_con.report_table} (serialno, phase, report_content, report_time) VALUES (%s, %s, %s, %s)"
            db_con.execute_sql_withdf((serialno, phase, report_content, datetime.now()), insert_sql)
        except Exception as e:
            an_logger.error(f"保存报告到数据库失败: {e}")
            raise
        finally:
            if db_con:
                db_con.close()
    
    def _determine_phase(self, intention_type: str) -> Optional[str]:
        """根据意图类型确定阶段"""
        if intention_type in self.ACTIVE_PHASE_INTENTS:
            return "ACTIVE"
        elif intention_type in self.CLEARED_PHASE_INTENTS:
            return "CLEARED"
        return None

