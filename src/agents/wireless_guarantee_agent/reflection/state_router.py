import threading
import time
from an_copilot.framework.logging import an_logger
import json
from src.core.db_connector import DbConn


class GlobalRegistry:
    """
    管理所有状态机实例的全局注册表
    提供内存缓存、TTL过期和LRU清理功能
    """
    _instances = {}  # {instance_id: {'instance': obj, 'last_access': timestamp}}
    _lock = threading.Lock()
    _max_instances = 1000  # 最大缓存实例数
    _ttl_seconds = 3600    # 实例TTL：1小时
    _lru_cleanup_ratio = 0.25  # LRU清理比例

    @classmethod
    def register(cls, instance_id: str, instance):
        """注册状态机实例到内存缓存"""
        with cls._lock:
            # 检查是否需要清理过期实例
            cls._cleanup_expired_instances()

            # 检查是否超过最大实例数，执行LRU清理
            if len(cls._instances) >= cls._max_instances:
                cls._cleanup_lru_instances()

            cls._instances[instance_id] = {
                'instance': instance,
                'last_access': time.time()
            }
            an_logger.info(f"实例{instance_id}注册完成，当前缓存实例数: {len(cls._instances)}")

    @classmethod
    def get_instance(cls, instance_id: str):
        """获取状态机实例，更新访问时间"""
        with cls._lock:
            entry = cls._instances.get(instance_id)
            if not entry:
                return None
                
            # 检查是否过期
            if time.time() - entry['last_access'] > cls._ttl_seconds:
                del cls._instances[instance_id]
                an_logger.info(f"实例{instance_id}已过期，从缓存中移除")
                return None

            # 更新访问时间
            entry['last_access'] = time.time()
            return entry['instance']

    @classmethod
    def get_all_instances(cls):
        """获取所有实例的副本"""
        with cls._lock:
            return {k: v['instance'] for k, v in cls._instances.items()}

    @classmethod
    def unregister(cls, instance_id: str):
        """手动注销实例"""
        with cls._lock:
            if instance_id in cls._instances:
                del cls._instances[instance_id]
                an_logger.info(f"实例{instance_id}注销完成")

    @classmethod
    def clear(cls):
        """清空所有注册的实例"""
        with cls._lock:
            count = len(cls._instances)
            cls._instances.clear()
            an_logger.info(f"已注销所有实例，共清理{count}个实例")

    @classmethod
    def _cleanup_expired_instances(cls):
        """清理过期的实例（内部方法，需要在锁内调用）"""
        current_time = time.time()
        expired_keys = []

        for instance_id, entry in cls._instances.items():
            if current_time - entry['last_access'] > cls._ttl_seconds:
                expired_keys.append(instance_id)

        for key in expired_keys:
            del cls._instances[key]

        if expired_keys:
            an_logger.info(f"清理了{len(expired_keys)}个过期实例: {expired_keys}")

    @classmethod
    def _cleanup_lru_instances(cls):
        """清理最近最少使用的实例（内部方法，需要在锁内调用）"""
        if len(cls._instances) < cls._max_instances:
            return

        # 按访问时间排序，移除最旧的实例
        sorted_items = sorted(cls._instances.items(), key=lambda x: x[1]['last_access'])
        cleanup_count = max(1, int(len(sorted_items) * cls._lru_cleanup_ratio))

        for i in range(cleanup_count):
            instance_id = sorted_items[i][0]
            del cls._instances[instance_id]

        an_logger.info(f"LRU清理了{cleanup_count}个最少使用的实例")

    @classmethod
    def get_cache_stats(cls):
        """获取缓存统计信息"""
        with cls._lock:
            current_time = time.time()
            total_count = len(cls._instances)
            expired_count = sum(1 for entry in cls._instances.values()
                                if current_time - entry['last_access'] > cls._ttl_seconds)

            return {
                'total_instances': total_count,
                'expired_instances': expired_count,
                'active_instances': total_count - expired_count,
                'max_instances': cls._max_instances,
                'ttl_seconds': cls._ttl_seconds
            }


class DatabasePersistenceManager:
    """基于数据库的状态机持久化管理器"""

    def get_state_info(self, sm_id: str):
        """从数据库查询状态机的状态信息"""
        return self._execute_with_connection(self._query_state_info, sm_id)
    
    def _execute_with_connection(self, operation, *args):
        """执行数据库操作的通用方法"""
        db_conn = None
        try:
            db_conn = DbConn()
            return operation(db_conn, *args)
        except Exception as e:
            an_logger.error(f"数据库操作失败: {e}")
            return None
        finally:
            if db_conn:
                db_conn.close()
    
    def _query_state_info(self, db_conn, sm_id: str):
        """查询状态信息的具体实现"""
        query_sql = f"""
            SELECT current_state, sm_context
            FROM {db_conn.alarm_table}
            WHERE sm_id = %s
        """
        result, col_names = db_conn.read_db_data(query_sql % f"'{sm_id}'")

        if not result or len(result) == 0:
            an_logger.info(f"数据库中未找到状态机 {sm_id} 的记录")
            return None

        row = result[0]
        current_state = row[0] if row[0] else None
        sm_context_json = row[1] if row[1] else None
        sm_context = self._parse_json_context(sm_context_json)

        an_logger.info(f"从数据库查询到状态机 {sm_id} 的状态信息: current_state={current_state}")
        return {
            'current_state': current_state,
            'sm_context': sm_context
        }
    
    def _parse_json_context(self, sm_context_json):
        """解析JSON上下文"""
        if not sm_context_json:
            return {}
        
        try:
            return json.loads(sm_context_json) if isinstance(sm_context_json, str) else sm_context_json
        except (json.JSONDecodeError, TypeError) as e:
            an_logger.warning(f"解析 sm_context JSON 失败: {e}")
            return {}


class StateMachineFactory:
    def __init__(self, state_machine_class):
        """
        初始化状态机工厂
        :param state_machine_class: 状态机类
        """
        self.state_machine_class = state_machine_class
        self.persistence_manager = DatabasePersistenceManager()

    def get_or_create(self, sm_id: str, sync_with_db: bool = False, **kwargs):
        """获取或创建状态机实例"""
        # 首先检查内存中是否已存在
        existing = GlobalRegistry.get_instance(sm_id)
        if existing is not None and not sync_with_db:
            an_logger.info(f"[{sm_id}] 状态机已存在于内存中")
            return existing

        # 从数据库查询最新状态
        an_logger.info(f"[{sm_id}] {'同步数据库状态' if existing else '状态机正在创建'}")
        state_info = self.persistence_manager.get_state_info(sm_id)

        if existing and sync_with_db:
            # 同步已存在实例的状态
            self._sync_existing_instance(existing, state_info, sm_id)
            return existing

        # 创建新实例
        instance = self._create_new_instance(sm_id, state_info, **kwargs)
        GlobalRegistry.register(sm_id, instance)
        an_logger.info(f"[{sm_id}] 状态机创建完成")
        return instance
    
    def _sync_existing_instance(self, instance, state_info, sm_id):
        """同步已存在实例的状态"""
        if not state_info or not state_info.get('current_state'):
            return
            
        current_state = state_info['current_state']
        sm_context = state_info.get('sm_context', {})

        try:
            self._apply_state_and_context(instance, current_state, sm_context, sm_id, "同步")
        except Exception as e:
            an_logger.warning(f"[{sm_id}] 同步状态失败: {e}")
    
    def _create_new_instance(self, sm_id, state_info, **kwargs):
        """创建新的状态机实例"""
        instance_kwargs = kwargs.copy()
        instance_kwargs.update({'sm_id': sm_id})
        instance = self.state_machine_class(**instance_kwargs)

        if state_info and state_info.get('current_state'):
            current_state = state_info['current_state']
            sm_context = state_info.get('sm_context', {})
            try:
                self._apply_state_and_context(instance, current_state, sm_context, sm_id, "恢复")
            except Exception as e:
                an_logger.warning(f"[{sm_id}] 恢复状态机状态失败，使用默认状态: {e}")
        else:
            an_logger.info(f"[{sm_id}] 数据库中无状态记录或状态为空，使用默认状态: recognizing")
        
        return instance
    
    def _apply_state_and_context(self, instance, current_state, sm_context, sm_id, operation_type):
        """应用状态和上下文到实例"""
        # 同步状态
        if instance.machine.state != current_state:
            instance.machine.set_state(current_state)
            an_logger.info(f"[{sm_id}] 状态已{operation_type}: {instance.machine.state} -> {current_state}")

        # 同步上下文
        if sm_context:
            if operation_type == "同步":
                instance.sm_context.clear()
            instance.sm_context.update(sm_context)
            an_logger.info(f"[{sm_id}] 上下文已{operation_type}")

    def get_cache_stats(self):
        """获取缓存统计信息"""
        return GlobalRegistry.get_cache_stats()

    def cleanup_cache(self):
        """手动清理缓存"""
        with GlobalRegistry._lock:
            GlobalRegistry._cleanup_expired_instances()
