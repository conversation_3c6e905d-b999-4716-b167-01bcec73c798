import json
import threading
import time
from datetime import datetime
from typing import Any

from an_copilot.framework.logging import an_logger
from transitions import Machine, EventData
from transitions.core import MachineError

from src.agents.wireless_guarantee_agent.desicion_making.goal_runner import GoalRunner
from src.agents.wireless_guarantee_agent.reflection.intent_recognition_chain import IntentRec<PERSON>ni<PERSON><PERSON>hain
from src.core.ces_stream_sender import CesStreamSender
from src.core.db_connector import DbConn


class WGStateMachine:
    # condition与btree node的对应关系
    condition_node_mapping = {
        "missing_mdt_data": "weak-cover",
        "no_effect": "weak_rate_no_effect",
        "common_site_compensation": "common-site",
        "empty_compensation_cells": "compensation-cell",
        "invalid_compensation_cells": "character-cell",
        "invalid_scheme": "compensation-scheme",
        "has_compensation_history": "task-state",
        "alarm_cleared": "is_evaluating_or_resetting",
    }
    
    # 可能流转到archived/interrupted的源状态
    TERMINAL_SOURCE_STATES = ["generating", "approving", "executing", "evaluating", "resetting", "rollback"]
    TERMINAL_DEST_STATES = ["interrupted", "archived"]

    def __init__(self, **kwargs: Any):
        # 初始化实例变量，避免多个实例间共享状态
        self.goal_runner: GoalRunner = None
        self.sm_context: dict = {}
        self.sm_id: str = kwargs.get("sm_id")
        # 延迟导入避免循环导入
        from src.config import settings
        self.ces_stream_sender: CesStreamSender = CesStreamSender(settings.ces_stream)
        self.session_id: str = None
        self.request_id: str = None

        # 初始化状态机
        self._btree_executed_in_transition = None
        self._init_state_machine()

    def _init_state_machine(self):
        """初始化状态机"""
        self.machine = Machine(
            model=self,
            states=self.states,
            transitions=self.transitions,
            initial="recognizing",
            auto_transitions=False,
            send_event=True,
            before_state_change="before_state_change",
            after_state_change="after_state_change",
        )

    def _validate_intention_state_compatibility(self, intention_type: str, current_state: str) -> bool:
        """
        验证意图类型与当前状态的兼容性
        """
        intention_state_mapping = {
            IntentRecognitionChain.INTENT_ACTIVE_WORKFLOW: ["recognizing"],
            IntentRecognitionChain.INTENT_APPROVAL_PROCESS: ["approving"],
            IntentRecognitionChain.INTENT_EXECUTE_PROCESS: ["executing"],
            IntentRecognitionChain.INTENT_EVALUATE_PROCESS: ["evaluating"],
            IntentRecognitionChain.INTENT_ROLLBACK_PROCESS: ["resetting"],
        }

        allowed_states = intention_state_mapping.get(intention_type, [])
        if not allowed_states:
            an_logger.warning(f"未知的意图类型: {intention_type}")
            return False

        is_compatible = current_state in allowed_states
        if not is_compatible:
            an_logger.warning(
                f"意图类型 {intention_type} 不兼容当前状态 {current_state}，"
                f"允许的状态: {allowed_states}"
            )

        return is_compatible

    def start(self, inputs: dict):
        """
        状态机的唯一对外入口方法
        根据意图类型触发相应的状态转换
        """
        # 记录状态机当前状态
        current_state = self._get_current_state()
        an_logger.info(f"状态机启动，当前状态: {current_state}")

        intention_type = inputs.get("intention_type")
        if inputs:
            self.sm_context.update(inputs)
            self.session_id = inputs.get("session_id")
            self.request_id = inputs.get("request_id")

        # 验证意图类型与当前状态的兼容性（除了清除告警可以从任意状态触发）
        if intention_type != IntentRecognitionChain.INTENT_CLEAR_WORKFLOW:
            if not self._validate_intention_state_compatibility(intention_type, current_state):
                return

        # 活动告警
        if intention_type == IntentRecognitionChain.INTENT_ACTIVE_WORKFLOW:
            if self._safe_trigger("receive"):
                self._safe_trigger("submit")
        # 审核结果
        elif intention_type == IntentRecognitionChain.INTENT_APPROVAL_PROCESS:
            self._safe_trigger("approve")
        # 执行结果
        elif intention_type == IntentRecognitionChain.INTENT_EXECUTE_PROCESS:
            self._safe_trigger("execute")
        # 评估结果
        elif intention_type == IntentRecognitionChain.INTENT_EVALUATE_PROCESS:
            self._safe_trigger("evaluate")
        # 清除告警
        elif intention_type == IntentRecognitionChain.INTENT_CLEAR_WORKFLOW:
            # 不管当前状态是什么，都触发复位流程
            # 状态机会根据条件自动选择合适的转换
            self._safe_trigger("receive_clear")
        # 回退结果
        elif intention_type == IntentRecognitionChain.INTENT_ROLLBACK_PROCESS:
            self._safe_trigger("rollback")

    def before_state_change(self, event):
        an_logger.info(f"transforming: {event.transition.source} -> {event.transition.dest}")

        # 记录触发状态流转的条件名称（用于sub_state字段）
        if (event.transition.source in self.TERMINAL_SOURCE_STATES and
            event.transition.dest in self.TERMINAL_DEST_STATES):
            self._triggered_condition = self._get_triggered_condition_name(event)
            an_logger.info(
                f"检测到需要记录sub_state的转换: {event.transition.source} -> {event.transition.dest}, 触发条件: {self._triggered_condition}")
        else:
            self._triggered_condition = None

    def _get_triggered_condition_name(self, event):
        """
        获取触发状态流转的条件名称

        该方法用于从transitions库的event对象中提取实际触发的条件名称，
        主要用于记录状态流转的sub_state字段。

        Args:
            event: transitions库的EventData对象，包含状态转换信息

        Returns:
            str: 触发的条件名称，如果无法确定则返回None

        Note:
            transitions库在内部会将字符串条件转换为Condition对象，
            因此需要处理多种数据类型：
            1. 字符串：直接使用
            2. Condition对象：提取其func属性
            3. 列表：取第一个元素进行处理
        """
        conditions = event.transition.conditions
        an_logger.debug(f"分析转换条件: {event.transition.source} -> {event.transition.dest}, conditions: {conditions}")
        if not conditions:
            an_logger.debug("转换没有条件，返回None")
            return None

        condition_name = None

        # 处理不同类型的conditions
        if isinstance(conditions, str):
            # 简单字符串条件
            condition_name = conditions
        elif isinstance(conditions, list) and len(conditions) > 0:
            first_condition = conditions[0]

            if hasattr(first_condition, 'func'):
                # transitions库的Condition对象
                # Condition对象的func属性可能是字符串或函数对象
                func = first_condition.func
                if isinstance(func, str):
                    # func是字符串（条件方法名），直接使用
                    condition_name = func
                elif callable(func):
                    # func是函数对象，获取函数名
                    condition_name = func.__name__
                else:
                    # 其他情况，转换为字符串
                    condition_name = str(func)
            elif isinstance(first_condition, str):
                # 列表中的字符串条件
                condition_name = first_condition
            else:
                # 未知类型的Condition对象，尝试从字符串表示中解析
                # 格式通常为: "<Condition(no_effect)@4733389200>"
                condition_str = str(first_condition)
                if 'Condition(' in condition_str and ')' in condition_str:
                    start = condition_str.find('Condition(') + len('Condition(')
                    end = condition_str.find(')', start)
                    if end > start:
                        condition_name = condition_str[start:end]

        if not condition_name:
            return None

        # 验证条件是否确实被触发
        # 这一步确保我们返回的条件名称确实对应一个被触发的条件方法
        try:
            condition_method = getattr(self, condition_name, None)
            if condition_method and callable(condition_method):
                # 需要检查这是conditions还是unless条件
                # 从transition的conditions列表中找到对应的Condition对象
                triggered_condition = None
                for cond in conditions:
                    if hasattr(cond, 'func'):
                        func = cond.func
                        func_name = func if isinstance(func, str) else (func.__name__ if callable(func) else str(func))
                        if func_name == condition_name:
                            triggered_condition = cond
                            break

                if triggered_condition:
                    # 调用条件方法并判断是否被触发
                    condition_result = condition_method(event)  

                    # 检查条件是否为 unless 条件（通过 target 属性判断）
                    # 在 transitions 库中，unless 条件会被转换为 target=False 的 Condition 对象
                    is_unless_condition = hasattr(triggered_condition, 'target') and not triggered_condition.target

                    an_logger.debug(
                        f"条件 {condition_name} 执行结果: {condition_result}, 是否为unless条件: {is_unless_condition}")

                    # 根据条件类型判断是否被触发
                    if is_unless_condition:
                        # unless 条件：返回 False 时触发转换
                        condition_triggered = not condition_result
                    else:
                        # 正常条件：返回 True 时触发转换
                        condition_triggered = condition_result

                    if condition_triggered:
                        # 条件被触发（满足转换条件）
                        an_logger.debug(f"条件 {condition_name} 被触发")
                        # 对于unless条件，进行语义转换
                        if is_unless_condition:
                            converted_name = _convert_unless_condition_name(condition_name)
                            an_logger.debug(f"unless条件语义转换: {condition_name} -> {converted_name}")
                            return converted_name
                        else:
                            return condition_name
                    else:
                        # 条件未被触发（不满足转换条件）
                        an_logger.debug(f"条件 {condition_name} 未被触发")
                        pass
                else:
                    # 如果找不到对应的Condition对象，直接返回条件名称
                    an_logger.debug(f"未找到条件对象，直接返回条件名称: {condition_name}")
                    return condition_name
        except Exception as e:
            an_logger.warning(f"检查条件 {condition_name} 时出错: {e}")

        return None

    def after_state_change(self, event: EventData):
        an_logger.info(f"transformed: {event.transition.source} -> {event.transition.dest}")
        db_conn = None
        try:
            db_conn = DbConn()
            serialno = (
                self.sm_context.get("serialno")
                if isinstance(self.sm_context, dict)
                else None
            )
            sm_id = self.sm_id
            source_state = event.transition.source
            target_state = event.transition.dest
            transition_time = datetime.now()
            btree_code = None
            btree_context_json = None

            # ==== 动态判定本次transition是否有btree执行 ====
            # 只要本次状态流转过程中有btree_executor.execute()被调用（即_execute_btree_action中设置了标志），
            # 则记录btree_code和btree_context等相关字段。记录后立即重置标志，避免影响下次流转。
            if hasattr(self,
                       '_btree_executed_in_transition') and self._btree_executed_in_transition and self.goal_runner:
                btree_code = self.goal_runner.btree_code
                if self.goal_runner.context:
                    context_data = self.goal_runner.context.get_data()
                    if context_data:
                        btree_context_json = json.dumps(context_data)
                # 记录后重置标志
                self._btree_executed_in_transition = False
            # ==== END 动态判定 ====

            sql = f"""
                INSERT INTO {db_conn.state_history_table}
                (serialno, sm_id, source_state, target_state, transition_time, btree_code, btree_context)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            data = (
                serialno,
                sm_id,
                source_state,
                target_state,
                transition_time,
                btree_code,
                btree_context_json,
            )
            db_conn.execute_sql_withdf(data, sql)
            an_logger.info(
                f"状态流转历史已记录到数据库: {serialno} from {source_state} to {target_state}"
            )
            # 同时更新tm_alarm_task_info表中的current_state字段和sm_context
            if serialno and target_state:
                # 准备 sm_context JSON 数据
                sm_context_json = json.dumps(self.sm_context) if self.sm_context else None

                # 从状态机上下文中获取 scheme_id
                scheme_id = self.sm_context.get("scheme_id") if isinstance(self.sm_context, dict) else None

                # 更新tm_alarm_task_info表
                self._update_alarm_task_info(db_conn, serialno, source_state, target_state, 
                                            transition_time, sm_context_json, scheme_id)
        except Exception as e:
            an_logger.error(f"记录状态流转历史到数据库失败: {e}")
        finally:
            if db_conn:
                db_conn.close()

    states = [
        "recognizing",  # 问题识别中
        "generating",  # 方案生成中
        "approving",  # 方案审核中
        "executing",  # 方案执行中
        "evaluating",  # 效果评估中
        "evaluated",  # 已评估通过
        "resetting",  # 方案复位中
        "interrupted",  # 已中断
        "archived",  # 已归档
    ]

    transitions = [
        {
            "trigger": "receive",
            "source": "recognizing",
            "dest": "generating",
            "after": "start_generate_scheme_process",
        },
        {
            "trigger": "submit",
            "source": "generating",
            "dest": "archived",
            "conditions": ["no_effect"],
        },
        {
            "trigger": "submit",
            "source": "generating",
            "dest": "archived",
            "conditions": ["common_site_compensation"],
        },
        {
            "trigger": "submit",
            "source": "generating",
            "dest": "interrupted",
            "conditions": ["missing_mdt_data"],
        },
        {
            "trigger": "submit",
            "source": "generating",
            "dest": "interrupted",
            "conditions": ["empty_compensation_cells"],
        },
        {
            "trigger": "submit",
            "source": "generating",
            "dest": "interrupted",
            "conditions": ["invalid_compensation_cells"],
        },
        {
            "trigger": "submit",
            "source": "generating",
            "dest": "interrupted",
            "conditions": ["invalid_scheme"],
        },
        {
            "trigger": "submit",
            "source": "generating",
            "dest": "approving",
            "conditions": ["generate_scheme_success"],
            "after": "start_approval_process",
        },
        {
            "trigger": "approve",
            "source": "approving",
            "dest": "executing",
            "conditions": ["scheme_approved"],
            "after": "start_execute_process",
        },
        {
            "trigger": "approve",
            "source": "approving",
            "dest": "archived",
            "unless": ["scheme_approved"],
        },
        {
            "trigger": "execute",
            "source": "executing",
            "dest": "evaluating",
            "conditions": ["execute_scheme_success"],
            "after": "start_evaluate_process",
        },
        {
            "trigger": "execute",
            "source": "executing",
            "dest": "interrupted",
            "unless": ["execute_scheme_success"],
        },
        {
            "trigger": "evaluate",
            "source": "evaluating",
            "dest": "resetting",
            "conditions": ["evaluate_scheme_success"],
        },
        {
            "trigger": "evaluate",
            "source": "evaluating",
            "dest": "interrupted",
            "unless": ["evaluate_scheme_success"],
            "after": "start_force_rollback_process",
        },
        # intention_type=5: 清除告警，从任意状态触发复位流程
        # 执行一次start_reset_process，然后根据结果连续判断条件决定最终状态
        {
            "trigger": "receive_clear",
            "source": ["recognizing", "generating", "approving", "executing", "evaluating", "evaluated", "resetting", "interrupted"],
            "dest": "resetting",
            "prepare": "start_reset_process",
            "conditions": ["has_compensation_history"],
            "after": "determine_final_clear_state",
        },
        # 内部transition：从resetting到archived（当alarm_cleared为True时）
        {
            "trigger": "auto_archive_after_clear",
            "source": "resetting",
            "dest": "archived",
        },
        # intention_type=6: 回退结果
        {
            "trigger": "rollback",
            "source": "resetting",
            "dest": "archived",
            "conditions": ["rollback_scheme_success"],
        },
        {
            "trigger": "rollback",
            "source": "resetting",
            "dest": "interrupted",
            "unless": ["rollback_scheme_success"],
            "after": "start_force_rollback_process",
        },
    ]

    # 简化的布尔结果检查方法
    def scheme_approved(self, event):
        return self._check_result("approval_result")

    def execute_scheme_success(self, event):
        return self._check_result("execute_result")

    def evaluate_scheme_success(self, event):
        return self._check_result("evaluate_result")

    def rollback_scheme_success(self, event):
        return self._check_result("rollback_result")

    # 方案生成成功
    def generate_scheme_success(self, event):
        """
        判断方案是否生成成功，只有当所有关键节点都正常执行时才返回True

        逻辑说明：
        - 所有节点返回True表示正常/成功情况
        - 任何节点返回False表示异常/失败情况
        - 只有当所有节点都成功时，方案才算生成成功
        """
        node_results = self.sm_context.get("node_results")
        if not node_results:
            an_logger.warning("没有找到节点执行结果")
            return False

        # 初始化最终结果为True
        final_result = True

        # 遍历所有节点结果，检查是否有失败的节点
        for node_name, node_result in node_results.items():
            final_result = final_result and node_result

        return final_result

    def missing_mdt_data(self, event):
        """判断是否缺少MDT数据"""
        return self._check_node_condition("missing_mdt_data")

    def no_effect(self, event):
        """判断空洞是否无影响"""
        return self._check_node_condition("no_effect")

    def common_site_compensation(self, event):
        """判断是否可以通过共站补偿解决问题"""
        return self._check_node_condition("common_site_compensation")

    def empty_compensation_cells(self, event):
        """判断是否无补偿小区"""
        return self._check_node_condition("empty_compensation_cells")

    def invalid_compensation_cells(self, event):
        """判断补偿小区是否可用"""
        return self._check_node_condition("invalid_compensation_cells")

    def invalid_scheme(self, event):
        """判断方案是否无效"""
        return self._check_node_condition("invalid_scheme")

    # 执行过补偿任务
    def has_compensation_history(self, event):
        return self._get_node_result("has_compensation_history")

    # 补偿任务状态为evaluating或resetting
    def alarm_cleared(self, event):
        return self._get_node_result("alarm_cleared")

    # Actions
    # 生成方案：从settings配置中获取generating对应的btree_code，用于初始化wg_btree_executor_chain并执行行为树
    def start_generate_scheme_process(self, event: EventData):
        an_logger.info("start generate scheme")
        # self.ces_stream_sender.send_msg(self.session_id, self.request_id, "开始生成方案")
        self._run_btree(event)

    # 复位
    def start_reset_process(self, event: EventData):
        an_logger.info("start reset")
        # self.ces_stream_sender.send_msg(self.session_id, self.request_id, "开始重置流程")
        self._run_btree(event)

    # 审核方案
    def start_approval_process(self, event: EventData):
        an_logger.info("start approval process")
        # self.ces_stream_sender.send_msg(self.session_id, self.request_id, "等待审核结果")
        """
        如果有外部审核系统，则调用外部审核系统触发审核流程
        如果内部审核，则无需做操作
        审核结果通过智能体入口进行意图归类后直接触发approve，流转到下一个状态
        """

    # 执行方案
    def start_execute_process(self, event: EventData):
        an_logger.info("start execute process")
        # self.ces_stream_sender.send_msg(self.session_id, self.request_id, "等待执行结果")
        """
        如果有外部执行系统，则调用外部执行系统触发执行流程
        如果内部执行，则无需做操作
        执行结果通过智能体入口进行意图归类后直接触发execute，流转到下一个状态
        """

    # 评估方案
    def start_evaluate_process(self, event: EventData):
        an_logger.info("start evaluate process")
        # self.ces_stream_sender.send_msg(self.session_id, self.request_id, "等待评估结果")
        """
        如果有外部评估系统，则调用外部评估系统触发评估流程
        如果内部评估，则无需做操作
        评估结果通过智能体入口进行意图归类后直接触发evaluate，流转到下一个状态
        """

    # 强制复位
    def start_force_rollback_process(self, event: EventData):
        an_logger.info("start force rollback process")
        # self.ces_stream_sender.send_msg(self.session_id, self.request_id, "等待强制回滚结果")
        """
        如果有外部回滚系统，则调用外部回滚系统触发回滚流程
        如果内部回滚，则无需做操作
        回滚结果通过智能体入口进行意图归类后直接触发rollback，流转到下一个状态
        """

    def determine_final_clear_state(self, event: EventData):
        """
        根据复位流程执行结果确定最终状态

        在start_reset_process执行完成后，根据alarm_cleared条件决定最终状态：
        - alarm_cleared=True: 触发auto_archive_after_clear转到archived状态
        - alarm_cleared=False: 保持在resetting状态

        使用异步延迟检查避免与transitions库回调执行顺序冲突
        """
        an_logger.info("根据复位流程结果确定最终状态")

        # 使用异步方式延迟检查，确保当前转换的所有回调完全完成

        def delayed_alarm_check():
            """延迟检查alarm_cleared条件，避免回调时序冲突"""
            try:
                # 短暂延迟确保当前状态转换的所有回调完成
                time.sleep(0.1)

                # 重新检查alarm_cleared条件
                if not self.alarm_cleared(event):
                    an_logger.info("告警已清除，触发转到archived状态")
                    # 使用trigger确保回调被正确执行
                    self.auto_archive_after_clear()
                else:
                    an_logger.info("告警未清除，保持在resetting状态")
                    # 已经在resetting状态，无需额外操作

            except Exception as e:
                an_logger.error(f"延迟状态检查过程中发生异常: {e}")

        # 启动异步线程执行延迟检查
        check_thread = threading.Thread(target=delayed_alarm_check, daemon=True)
        check_thread.start()

        an_logger.info("已启动异步状态检查线程")

    def _run_btree(self, event: EventData):
        """
        根据action_name获取btree_code，并执行行为树
        支持从prepare、before、after等回调中获取action_name
        """
        action_name = _get_action_name_from_event(event)
        if not action_name:
            an_logger.warning("No valid callback found for btree execution.")
            return

        # 延迟导入避免循环导入
        from src.config import settings
        btree_code = settings.config.wg_agent_config.btree_codes.get(action_name)
        if btree_code:
            an_logger.info(f"action: '{action_name}' BTree code: '{btree_code}'")
            self.goal_runner = GoalRunner(btree_code=btree_code)
            # ==== 设置本次transition有btree执行的标志 ====
            # 只要本次状态流转中实际执行了btree（即调用了execute），就设置标志，供after_state_change判断
            self._btree_executed_in_transition = True
            # ==== END 标志设置 ====
            self.goal_runner.run(inputs=self.sm_context)
            self.sm_context.update(self.goal_runner.context.get_data())
        else:
            an_logger.warning(f"No BTree code found for action: '{action_name}'")

    def _check_result(self, key: str):
        """
        按key检查状态机上下文

        Args:
            key: 上下文中的键名

        Returns:
            value or None: 如果key存在则返回对应的value，否则返回None
        """
        if key in self.sm_context:
            return self.sm_context[key]
        return None

    def _get_node_result(self, condition_name: str):
        """获取节点执行结果"""
        node_name = self.condition_node_mapping.get(condition_name)
        if not node_name:
            return None

        node_results = self.sm_context.get("node_results")
        if not node_results or node_name not in node_results:
            return None

        return node_results[node_name]
    
    def _check_node_condition(self, condition_name: str) -> bool:
        """检查节点条件，返回反向结果"""
        return not self._get_node_result(condition_name)
    
    def _get_current_state(self) -> str:
        """获取当前状态"""
        return getattr(self, 'state', None) or (
            self.machine.state if hasattr(self, 'machine') else 'unknown')
    
    def _update_alarm_task_info(self, db_conn, serialno, source_state, target_state, 
                               transition_time, sm_context_json, scheme_id):
        """更新tm_alarm_task_info表"""
        if (source_state in self.TERMINAL_SOURCE_STATES and
            target_state in self.TERMINAL_DEST_STATES and
            hasattr(self, '_triggered_condition') and
            self._triggered_condition):
            
            # 更新包含sub_state的字段
            update_sql = f"""
                UPDATE {db_conn.alarm_table}
                SET current_state = %s, state_time = %s, sub_state = %s, sm_context = %s, scheme_id = %s
                WHERE serialno = %s
            """
            db_conn.execute_sql_withdf(
                (target_state, transition_time, self._triggered_condition, sm_context_json, scheme_id, serialno),
                update_sql)
            an_logger.info(
                f"tm_alarm_task_info表已更新: {serialno} -> {target_state}, {transition_time}, sub_state: {self._triggered_condition}, sm_context已更新, scheme_id: {scheme_id}")
        else:
            # 更新不包含sub_state的字段
            an_logger.debug(
                f"不更新sub_state字段 - source_state: {source_state}, target_state: {target_state}, "
                f"has_triggered_condition: {hasattr(self, '_triggered_condition')}, "
                f"triggered_condition: {getattr(self, '_triggered_condition', 'N/A')}")
            update_sql = f"""
                UPDATE {db_conn.alarm_table}
                SET current_state = %s, state_time = %s, sm_context = %s, scheme_id = %s
                WHERE serialno = %s
            """
            db_conn.execute_sql_withdf((target_state, transition_time, sm_context_json, scheme_id, serialno), update_sql)
            an_logger.info(
                f"tm_alarm_task_info表current_state, state_time, sm_context和scheme_id已更新: {serialno} -> {target_state}, {transition_time}, scheme_id: {scheme_id}")

    def _safe_trigger(self, trigger_name: str, event_name: str = None):
        """
        安全地触发状态转换，捕获并记录MachineError异常

        Args:
            trigger_name: 触发器名称
            event_name: 事件名称（用于日志记录，如果为None则使用trigger_name）

        Returns:
            bool: 转换是否成功
        """
        if event_name is None:
            event_name = trigger_name

        try:
            # 获取触发器方法并调用
            trigger_method = getattr(self, trigger_name, None)
            if trigger_method and callable(trigger_method):
                return trigger_method()
            else:
                an_logger.error(f"触发器方法不存在: {trigger_name}")
                return False
        except MachineError as e:
            # 记录无效状态转换的错误
            current_state = self._get_current_state()
            an_logger.error(f"无效状态转换 - 当前状态: {current_state}, 事件: {event_name}, 错误信息: {str(e)}")
            # 向用户发送错误消息
            if self.session_id and self.request_id:
                self.ces_stream_sender.send_msg(
                    self.session_id,
                    self.request_id,
                    f"状态转换失败: 当前状态为{current_state}，无法执行{event_name}操作"
                )
            return False
        except Exception as e:
            # 记录其他异常
            current_state = self._get_current_state()
            an_logger.error(f"状态转换过程中发生异常 - 当前状态: {current_state}, 事件: {event_name}, 异常: {str(e)}")
            return False


def _get_action_name_from_event(event: EventData):
    """
    从event中获取action_name，支持多种回调类型
    优先级：prepare > before > after
    """
    # 检查prepare回调
    if hasattr(event.transition, 'prepare') and event.transition.prepare:
        callbacks = event.transition.prepare
        action_name = callbacks[0] if isinstance(callbacks, list) else callbacks
        if callable(action_name):
            action_name = action_name.__name__
        return action_name

    # 检查before回调
    if hasattr(event.transition, 'before') and event.transition.before:
        callbacks = event.transition.before
        action_name = callbacks[0] if isinstance(callbacks, list) else callbacks
        if callable(action_name):
            action_name = action_name.__name__
        return action_name

    # 检查after回调
    if hasattr(event.transition, 'after') and event.transition.after:
        callbacks = event.transition.after
        action_name = callbacks[0] if isinstance(callbacks, list) else callbacks
        if callable(action_name):
            action_name = action_name.__name__
        return action_name

    return None


def _convert_unless_condition_name(condition_name):
    """
    将unless条件名称转换为更直观的失败原因描述

    Args:
        condition_name: 原始条件名称

    Returns:
        str: 转换后的条件名称
    """
    # 定义转换映射表
    conversion_map = {
        # 审核相关
        "scheme_approved": "scheme_rejected",

        # 执行相关
        "execute_scheme_success": "execute_scheme_failed",

        # 评估相关
        "evaluate_scheme_success": "evaluate_scheme_failed",

        # 回滚相关
        "rollback_scheme_success": "rollback_scheme_failed",
    }

    # 如果有明确的映射，使用映射值
    if condition_name in conversion_map:
        return conversion_map[condition_name]

    # 通用转换规则
    if condition_name.endswith("_success"):
        return condition_name.replace("_success", "_failed")
    elif condition_name.endswith("_approved"):
        return condition_name.replace("_approved", "_rejected")
    elif "success" in condition_name:
        return condition_name.replace("success", "failed")
    elif "approved" in condition_name:
        return condition_name.replace("approved", "rejected")
    else:
        # 如果没有匹配的规则，添加_failed后缀
        return f"{condition_name}_failed"
