import json
from typing import Any, Dict, List, Optional, ClassVar

from an_copilot.framework.chain.copilot_chain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LLmGeneration
from an_copilot.framework.logging import an_logger
from langchain.prompts import PromptTemplate
from langchain_core.callbacks import CallbackManagerForChainRun
from src.core.db_connector import DbConn
from datetime import datetime

from src.core.ces_stream_sender import CesStreamSender
from src.utils.knowledge_util import similarity_search, generate_content, save_report_to_db

import time
from datetime import datetime

class IntentRecognitionChain(CopilotChain):
    """
    无线保障智能体的意图识别链

    主要功能：
    1. 解析输入的告警信息（支持JSON格式和自然语言）
    2. 根据告警状态和类型进行意图分类
    3. 返回识别结果和标准化的告警信息

    支持的意图类型：
    - "1": 新活动工作流（活动告警+宏站）
    - "2": 审批流程（包含approval_user字段）
    - "3": 执行流程（包含execute_time字段）
    - "4": 评估流程（包含evaluate_time字段）
    - "5": 历史清除工作流（已清除告警+宏站）
    """
    ces_stream_sender: CesStreamSender = None

    # 意图类型常量 映射
    INTENT_ACTIVE_WORKFLOW: ClassVar[str] = "1"
    INTENT_APPROVAL_PROCESS: ClassVar[str] = "2"
    INTENT_EXECUTE_PROCESS: ClassVar[str] = "3"
    INTENT_EVALUATE_PROCESS: ClassVar[str] = "4"
    INTENT_CLEAR_WORKFLOW: ClassVar[str] = "5"
    INTENT_ROLLBACK_PROCESS: ClassVar[str] = "6"

    # 意图分类规则映射
    INTENT_RULES: ClassVar[List[tuple]] = [
        (lambda info: info.get("alarm_status") == "活动", INTENT_ACTIVE_WORKFLOW, "新活动工作流"),
        (lambda info: "approval_result" in info, INTENT_APPROVAL_PROCESS, "审批流程"),
        (lambda info: "execute_result" in info, INTENT_EXECUTE_PROCESS, "执行流程"),
        (lambda info: "evaluate_result" in info, INTENT_EVALUATE_PROCESS, "评估流程"),
        (lambda info: info.get("alarm_status") == "已清除", INTENT_CLEAR_WORKFLOW, "清除工作流"),
        (lambda info: "rollback_result" in info, INTENT_ROLLBACK_PROCESS, "回退流程"),
    ]


    MESSAGE_COMPLETE_SENDING_INTENTS: ClassVar[set] = {INTENT_ACTIVE_WORKFLOW, INTENT_CLEAR_WORKFLOW} # 完整走完6个阶段
    COMPLETE_INTENTS: ClassVar[set] = {"1", "5"}
    # 消息阶段配置
    ACTIVE_MESSAGE_STATES:ClassVar[list[str]] = ["状态感知", "意图识别", "目标管理", "任务规划"]

    # 知识库配置
    PUSH_REPOSITORY: ClassVar[list] = ["无线保障消息推送库"]

    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        # 延迟导入避免循环导入
        from src.config import settings
        self.ces_stream_sender = CesStreamSender(settings.ces_stream)

    def get_name(self) -> str:
        return "意图识别"

    def description(self) -> str:
        return "根据输入，进行意图识别，选择不同路径"

    @property
    def input_keys(self) -> List[str]:
        return ["input"]

    @property
    def output_keys(self) -> List[str]:
        return [self.output_key]

    def _call(
        self,
        inputs: Dict[str, Any],
        run_manager: Optional[CallbackManagerForChainRun] = None,
    ) -> Dict[str, str] | None:
        """
        意图识别链的主要执行方法

        Args:
            inputs: 输入参数，包含待解析的告警信息
            run_manager: 回调管理器，包含会话和请求信息

        Returns:
            包含识别结果和告警信息的响应字典

        Raises:
            ValueError: 当输入无法解析或意图无法识别时抛出
        """
        # 提取输入消息和请求上下文
        message = inputs.get("input")
        session_and_request_json = json.loads(run_manager.tags[0])
        request_id = session_and_request_json.get("request_id")
        # 安全解析告警信息（支持JSON和自然语言输入）
        alarm_info, input_type = self._parse_alarm_info_safely(request_id, message, inputs, run_manager)

        # 基于告警信息进行意图分类 返回的recognition 即 intent_type
        recognition = self._intent_classification(input_type, alarm_info, request_id, run_manager)
        if recognition is None:
            an_logger.error(f"无法识别的意图类型，流程终止: {message}")
            raise ValueError(f"无法识别的意图类型: {message}")
        # 构建并返回响应
        response = json.dumps({
            "input_type": input_type,
            "recognition": recognition,
            "alarm_info": alarm_info,
        }, ensure_ascii=False)
        an_logger.info(f"意图识别结果：{response}")
        return self.response_wrapper(LLmGeneration(content=response))
    
    # 解析输入相关函数
    def _parse_alarm_info_safely(self, request_id: str, message: str, inputs: Dict[str, Any],
                                 run_manager: Optional[CallbackManagerForChainRun] = None) -> tuple[Dict[str, Any], str]:
        """
        安全地解析告警信息，支持多种输入格式
        """
        # 第一步：尝试直接JSON解析
        if _is_valid_json(message):
            alarm_info = self._try_parse_json(message)
            if alarm_info:
                an_logger.debug("直接JSON解析成功")
                return alarm_info, "JSON"

        # 第二步：使用LLM辅助格式化
        an_logger.warning("直接解析失败，使用LLM辅助格式化")
        alarm_info = self._try_llm_format(inputs, run_manager, request_id)
        if alarm_info:
            return alarm_info, "UNSTRUCTURED"

        # 所有方法都失败，抛出异常
        raise ValueError(f"无法处理的输入内容: {message}")
    
    def _try_parse_json(self, message: str) -> Optional[Dict[str, Any]]:
        """尝试直接解析JSON"""
        try:
            alarm_info = json.loads(message, strict=False)
            if isinstance(alarm_info, dict):
                return alarm_info
        except (json.JSONDecodeError, Exception) as e:
            an_logger.debug(f"JSON解析失败: {e}")
        return None
    
    def _try_llm_format(self, inputs: Dict[str, Any], run_manager, request_id: str) -> Optional[Dict[str, Any]]:
        """尝试使用LLM格式化输入"""
        try:
            formatted_content = self._try_format_input(inputs, run_manager)
            an_logger.info(f"LLM辅助格式化输出: {formatted_content}")
            alarm_info = json.loads(formatted_content, strict=False)
            if isinstance(alarm_info, dict):
                return alarm_info
        except Exception as e:
            an_logger.error(f"LLM辅助格式化失败：{e}")
            self.ces_stream_sender.send_msg(self.session_id, request_id, f"请检查输入内容: {formatted_content}")
        return None

    def _try_format_input(self, inputs: Dict[str, Any], run_manager: Optional[CallbackManagerForChainRun] = None) -> str:
        """使用LLM辅助格式化输入内容"""
        # 延迟导入避免循环导入
        from src.config import settings
        prompt = settings.get_prompts_factory().get_prompt_template(
            prompt_template_name="INFO_EXTRACTION_PROMPT",
            prompt_id=settings.config.llm.get_prompt_id(),
        )
        prompt_value = prompt.format_prompt(**inputs)
        response = self.llm_generate(
            messages=[prompt_value.to_messages()], run_manager=run_manager
        )
        return response.content
    

    # 意图识别相关函数
    def _intent_classification(self, input_type: str, alarm_info: Dict[str, Any], request_id: str,
                             run_manager: Optional[CallbackManagerForChainRun]) -> Optional[str]:
        """基于告警信息进行意图分类"""
        # 遍历意图规则进行匹配 匹配每个可能的意图类型
        # lambda表达式 匹配输入的报警信息(字典形式)
        for rule_func, intent_type, description in self.INTENT_RULES:
            if rule_func(alarm_info):
                self._log_intent_recognition(alarm_info, intent_type, description) # 记录意图识别结果
                return intent_type
        # 无法识别意图
        an_logger.warning(f"无法识别意图类型，告警信息：{alarm_info}")
        return None
    
    # 记录意图识别结果相关函数
    def _log_intent_recognition(self, alarm_info: Dict[str, Any], intent_type: str, description: str):
        """记录意图识别结果"""
        serialno = alarm_info.get('serialno')
        if intent_type == self.INTENT_ACTIVE_WORKFLOW:
            an_logger.info(f"识别为{description}，告警流水号：{serialno}")
        else:
            result_key = self._get_result_key_for_intent(intent_type)
            result_value = alarm_info.get(result_key, '未知') if result_key else ''
            if result_value:
                an_logger.info(f"[{serialno}] 识别为{description}，{result_key.replace('_', '')}：{result_value}")
            else:
                an_logger.info(f"[{serialno}] 识别为{description}")
    
    def _get_result_key_for_intent(self, intent_type: str) -> Optional[str]:
        """根据意图类型获取结果字段名"""
        result_mapping = {
            self.INTENT_APPROVAL_PROCESS: "approval_result",
            self.INTENT_EXECUTE_PROCESS: "execute_result",
            self.INTENT_EVALUATE_PROCESS: "evaluate_result",
            self.INTENT_ROLLBACK_PROCESS: "rollback_result",
        }
        return result_mapping.get(intent_type)

# 类外的辅助函数
def _is_valid_json(text: str) -> bool:
    """
    快速检查文本是否可能是有效的JSON格式

    通过检查开始和结束字符来快速判断，避免不必要的解析尝试

    Args:
        text: 待检查的文本

    Returns:
        True如果文本可能是JSON格式，False否则
    """
    if not text or not isinstance(text, str):
        return False

    text = text.strip()
    # JSON对象或数组的基本格式检查
    return (text.startswith('{') and text.endswith('}')) or \
        (text.startswith('[') and text.endswith(']'))

