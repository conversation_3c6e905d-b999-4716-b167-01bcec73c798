import json
from typing import Any, Dict, List, Optional, ClassVar
from an_copilot.framework.chain.copilot_chain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LLmGeneration
from an_copilot.framework.logging import an_logger
from langchain_core.callbacks import CallbackManagerForChainRun
from src.config import settings
from src.core.service_query import query_cell_time


class InfoExtractionChain(CopilotChain):
    """根据意图，提取不同的信息"""
    def get_name(self) -> str:
        return "信息提取"

    def description(self) -> str:
        return "根据意图，提取不同的信息"

    @property
    def input_keys(self) -> List[str]:
        return ["question", "history"]

    @property
    def output_keys(self) -> List[str]:
        return [self.output_key]

    def _call(
        self,
        inputs: Dict[str, Any],
        run_manager: Optional[CallbackManagerForChainRun] = None,
    ) -> Dict[str, str]:
        prompt = settings.get_prompts_factory().get_prompt_template(
            "SERIVICE_INFO_EXTRACTION_PROMPT",
            prompt_id=settings.config.llm.get_prompt_id(),
        )
        prompt_value = prompt.format_prompt(**inputs)
        response = self.llm_generate(
            messages=[prompt_value.to_messages()], run_manager=run_manager
        )
        res_json = json.loads(response.content)
        try:
            res_json['start_time'] = query_cell_time(res_json['cell_name'])
            out_resp = json.dumps(res_json)
        except Exception as e:
            an_logger.error(f"InfoExtractionChain Exception：{e}")
            out_resp = response.content
        an_logger.info(f"InfoExtractionChain 输出： {out_resp}")
        return self.response_wrapper(LLmGeneration(content=out_resp))
