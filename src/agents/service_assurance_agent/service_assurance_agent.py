import json
import time
from typing import ClassVar, Dict, List, Optional

from an_contract.framework.agent_entity import AgentR<PERSON>ponse
from an_copilot.framework.agent.copilot_agent import CopilotAgent, HistoryMode
from an_copilot.framework.logging import an_logger
from langchain.callbacks.manager import Callbacks
from langchain.prompts import PromptTemplate

from src.agents.service_assurance_agent.desicion_making.goal_plan_chain import <PERSON><PERSON><PERSON><PERSON>hain
from src.agents.service_assurance_agent.reflection.history_task_chain import <PERSON><PERSON><PERSON><PERSON>hain
from src.agents.service_assurance_agent.reflection.intent_analysis_chain import IntentAnalysisChain
from src.agents.service_assurance_agent.reflection.status_perception_chain import StatusPerception<PERSON>hain
from src.config import settings
from src.core.ces_stream_sender import CesStreamSender
from src.agents.service_assurance_agent.chains.info_extraction_chain import InfoExtraction<PERSON>hain


class ServiceAssuranceAgent(CopilotAgent):
    name: str = "业务体验保障智能体"
    description: str = "业务体验保障智能体"
    history_mode = HistoryMode.ALL.value
    skills: ClassVar[List[str]] = [
        "业务体验保障智能体",
    ]

    def __init__(self, session_id: str):
        super().__init__(
            session_id=session_id,
            callbacks=settings.get_tracing_factory().getTracing(),
        )
        self.ces_stream_sender = CesStreamSender(settings.ces_stream)
        self.status_perception = StatusPerceptionChain.from_settings(
            session_id=self.session_id, settings=settings
        )
        self.history_task = HistoryTaskChain.from_settings(
            session_id=self.session_id, settings=settings
        )

        self.intent_analysis = IntentAnalysisChain.from_settings(
            session_id=self.session_id, settings=settings
        )
        self.goal_plan= GoalPlanChain.from_settings(
            session_id=self.session_id, settings=settings
        )
        self.info_extraction = InfoExtractionChain.from_settings(
            session_id=self.session_id, settings=settings
        )


    def _run(
        self,
        request_id: str,
        question: str,
        tags: Optional[List[str]] = None,
        user_agent_config: Optional[dict] = None,
        history: List[List[str]] = None,
        previous_agent_history: List[Dict[str, List[List[str]]]] = None,
        history_messages: Optional[List[Dict]] = None,
        previous_agent_history_messages: Optional[List[Dict[str, List[Dict]]]] = None,
        agent_contexts: List[str] = [],
        contexts: Optional[dict] = None,
        prompt_template: PromptTemplate = None,
        callbacks: Callbacks = None,
        reasoning: Optional[bool] = False,
    ) -> AgentResponse:
        response_wrapper = ""
        status_perception_resp = self.status_perception.run(
            question=question, history=None, tags=tags, callbacks=callbacks
        )
        an_logger.info(f"ServiceAssuranceAgent 环境感知结果：{status_perception_resp.value}")
        if "工单" in status_perception_resp.value and self.is_json(question):
            response_wrapper = self._handle_task(callbacks, question, request_id, response_wrapper, tags)
        else:
            info_extraction = self.info_extraction.run(question=question, history=None, tags=tags, callbacks=callbacks)
            an_logger.info(f"ServiceAssuranceAgent：问答  info_extraction : {info_extraction.value}  ")
            self.status_perception.run(
                question=info_extraction.value, history=None, tags=tags, callbacks=callbacks
            )
            response_wrapper = self._handle_task(callbacks, info_extraction.value, request_id, response_wrapper, tags)
        an_logger.info(f"ServiceAssuranceAgent： req: {question} -> resp:{response_wrapper}")
        return AgentResponse(
            agent_name=self.name,
            agent_input=question,
            agent_output=""
        )

    def _handle_task(self, callbacks, question, request_id, response_wrapper, tags):
        plan_res = self.history_task.run(
            question=question, history=None, tags=tags, callbacks=callbacks
        )
        an_logger.info(f"历史查询：{plan_res.value}")
        if "0" == plan_res.value:
            intent_resp = self.intent_analysis.run(question=question, history=None, tags=tags, callbacks=callbacks)
            # goal识别
            if "0" == intent_resp.value:
                btree_code = settings.config.service_agent_config.service_btree_code
                inputs = {}
                inputs["btree_code"] = btree_code
                inputs["question"] = question
                response_wrapper = self.goal_plan.run(
                    inputs, callbacks=callbacks, tags=tags
                )
            else:
                object_info = f""" \n### 任务规划 \n 规划中… """
                self.ces_stream_sender.send_msg(
                    self.session_id,
                    request_id,
                    message=object_info,
                    begin_time=time.time(),
                )
        return response_wrapper

    def is_json(self,input_str):
        try:
            parsed = json.loads(input_str)
            # 如果你只想判断是否为对象或数组（而不是字符串/数字），可以加限制：
            return isinstance(parsed, (dict, list))
        except Exception as e:
            return False


ServiceAssuranceAgent.register()
