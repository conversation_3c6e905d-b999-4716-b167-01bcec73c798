import json
import random
import time
from typing import Any, Dict, List, Optional
from an_contract.framework.stream_entity import (
    CesStreamFormat,
    CesStreamType,
    ChatStreamMessage,
)
from an_copilot.framework.logging import an_logger
from langchain_core.callbacks import CallbackManagerForChainRun
from an_copilot.framework.chain.copilot_chain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LLmGeneration
from langchain_core.prompts import PromptTemplate

from src.core.ces_stream_sender import CesStreamSender
from src.config import settings
from src.core.service_query import query_video_low_cellnum
from src.utils.knowledge_util import similarity_search

class IntentAnalysisChain(CopilotChain):
    """
    意图分析
    """
    ces_stream_sender: CesStreamSender = None
    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        self.ces_stream_sender = CesStreamSender(settings.ces_stream)


    def get_name(self) -> str:
        return "意图分析"

    def description(self) -> str:
        return "这是根据用户输入问题，分析用户的意图"

    @property
    def input_keys(self) -> List[str]:
        return ["question", "history"]

    @property
    def output_keys(self) -> List[str]:
        return [self.output_key]

    def _call(
        self,
        inputs: Dict[str, Any],
        run_manager: Optional[CallbackManagerForChainRun] = None,
    ) -> Dict[str, str]:
        try:
            begin_time = time.time()
            session_and_request_json = json.loads(run_manager.tags[0])
            request_id = session_and_request_json.get("request_id")

            question = inputs.get("question")
            history = inputs.get("history")
            an_logger.info(f"IntentAnalysisChain 输入参数：question = {question}")
            an_logger.info(f"IntentAnalysisChain 输入参数：history = {history}")

            service_info = json.loads(question, strict=False)
            start_time = service_info.get("start_time")
            cell_name = service_info.get("cell_name")
            cell_data = query_video_low_cellnum(cell_name,start_time)
            if cell_data:
                inputs['object_name'] = cell_data['object_name']
                inputs['is_video_low'] = cell_data['is_video_low']
                inputs['video_low_cellnum']= cell_data['video_low_cellnum']
            else: ## 查询无数据
                inputs['is_video_low'] = -1
                inputs['video_low_cellnum'] = 0

            service_info.update(cell_data)
            inputs["service_info"] = service_info

            # prompt = settings.get_prompts_factory().get_prompt_template(
            #     "INTENT_ANALYSIS_CHAIN_PROMPT_TEMPLATE",
            #     prompt_id=settings.config.llm.get_prompt_id(),
            # )
            reference_documents_array = similarity_search(text='意图识别', repository_alias=['业务体验保障知识库'])
            reference_documents = reference_documents_array[0]
            an_logger.error(f"知识库返回   意图识别：{reference_documents}")
            prompt = PromptTemplate.from_template(reference_documents)

            prompt_value = prompt.format_prompt(**inputs)
            response = self.llm_generate(
                messages=[prompt_value.to_messages()], run_manager=run_manager
            )
            self.ces_stream_sender.send_msg(
                self.session_id, request_id,  response.content, begin_time=begin_time
            )

            if cell_data and cell_data['is_video_low'] == 1:
                recognition = '1'
            else:
                recognition = '0'

            # prompt = settings.get_prompts_factory().get_prompt_template(
            #     "OBJECT_OUTPUT_CHAIN_PROMPT_TEMPLATE",
            #     prompt_id=settings.config.llm.get_prompt_id(),
            # )


            reference_documents_array = similarity_search(text='目标管理', repository_alias=['业务体验保障知识库'])
            reference_documents = reference_documents_array[0]
            an_logger.error(f"知识库返回   目标管理：{reference_documents}")
            prompt = PromptTemplate.from_template(reference_documents)
            prompt_value = prompt.format_prompt(**inputs)

            response = self.llm_generate(
                messages=[prompt_value.to_messages()], run_manager=run_manager
            )
            self.ces_stream_sender.send_msg(
                self.session_id, request_id, response.content, begin_time=begin_time
            )
            return self.response_wrapper(LLmGeneration(content=recognition))
        except Exception as e:
            an_logger.error(f"DataInsightChain error: {e}")
            raise e


