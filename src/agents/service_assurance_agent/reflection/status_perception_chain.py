import json
import random
import time
from typing import Any, Dict, List, Optional
from an_contract.framework.stream_entity import (
    CesStreamFormat,
    CesStreamType,
    ChatStreamMessage,
)
from an_copilot.framework.logging import an_logger
from langchain_core.callbacks import CallbackManagerForChainRun
from an_copilot.framework.chain.copilot_chain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LLmGeneration
from langchain_core.prompts import PromptTemplate

from src.core.ces_stream_sender import CesStreamSender
from src.config import settings
from src.utils.knowledge_util import similarity_search



class StatusPerceptionChain(CopilotChain):
    """
    环境感知
    """
    ces_stream_sender: CesStreamSender = None
    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        self.ces_stream_sender = CesStreamSender(settings.ces_stream)


    def get_name(self) -> str:
        return "环境感知"

    def description(self) -> str:
        return "环境感知"

    @property
    def input_keys(self) -> List[str]:
        return ["question", "history"]

    @property
    def output_keys(self) -> List[str]:
        return [self.output_key]

    def _call(
        self,
        inputs: Dict[str, Any],
        run_manager: Optional[CallbackManagerForChainRun] = None,
    ) -> Dict[str, str]:
        begin_time = time.time()
        session_and_request_json = json.loads(run_manager.tags[0])
        request_id = session_and_request_json.get("request_id")
        question = inputs.get("question")
        prompt = settings.get_prompts_factory().get_prompt_template(
            "STATUS_PERCEPTION_CHAIN_PROMPT_TEMPLATE",
            prompt_id=settings.config.llm.get_prompt_id(),
        )
        prompt_value = prompt.format_prompt(**inputs)
        response = self.llm_generate(
            messages=[prompt_value.to_messages()], run_manager=run_manager
        )

        if "工单" in response.content:
            try:
                service_info = json.loads(question, strict=False)
                an_logger.info(f"StatusPerceptionChain 输入参数：question = {question}")
                if not isinstance(service_info, dict):
                    an_logger.error(f"输入不是合法的JSON格式：{service_info}")
                    return self.response_wrapper(LLmGeneration(content="问答"))
                # prompt = settings.get_prompts_factory().get_prompt_template(
                #     "STATUS_OUTPUT_CHAIN_PROMPT_TEMPLATE",
                #     prompt_id=settings.config.llm.get_prompt_id(),
                # )
                # prompt_value = prompt.format_prompt(**inputs)

                reference_documents_array = similarity_search(text= '状态感知',repository_alias= ['业务体验保障知识库'])
                reference_documents = reference_documents_array[0]
                an_logger.error(f"知识库返回   reference_documents：{reference_documents}")
                prompt = PromptTemplate.from_template(reference_documents)
                prompt_value = prompt.format_prompt(**inputs)
                out_response = self.llm_generate(
                    messages=[prompt_value.to_messages()], run_manager=run_manager
                )
                self.ces_stream_sender.send_msg(
                    self.session_id, request_id, out_response.content, begin_time=begin_time
                )

            except Exception as e:
                an_logger.error(f"输入不是合法的JSON格式 异常：{e}")
                return self.response_wrapper(LLmGeneration(content="问答"))
        return self.response_wrapper(response)
