import json
import random
import time
from typing import Any, Dict, List, Optional
from an_contract.framework.stream_entity import (
    CesStreamFormat,
    CesStreamType,
    ChatStreamMessage,
)
from an_copilot.framework.logging import an_logger
from langchain_core.callbacks import CallbackManagerForChainRun
from an_copilot.framework.chain.copilot_chain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LLmGeneration
from src.core.ces_stream_sender import CesStreamSender
from src.config import settings
from src.core.service_query import query_cell_log


class HistoryTaskChain(CopilotChain):
    """
    环境感知
    """
    ces_stream_sender: CesStreamSender = None
    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        self.ces_stream_sender = CesStreamSender(settings.ces_stream)


    def get_name(self) -> str:
        return "历史任务分析"

    def description(self) -> str:
        return "历史任务分析"

    @property
    def input_keys(self) -> List[str]:
        return ["question", "history"]

    @property
    def output_keys(self) -> List[str]:
        return [self.output_key]

    def _call(
        self,
        inputs: Dict[str, Any],
        run_manager: Optional[CallbackManagerForChainRun] = None,
    ) -> Dict[str, str]:
        session_and_request_json = json.loads(run_manager.tags[0])
        request_id = session_and_request_json.get("request_id")

        question = inputs.get("question")
        history = inputs.get("history")
        an_logger.info(f"HistoryTaskChain输入参数：question = {question}")
        an_logger.info(f"HistoryTaskChain输入参数：history = {history}")


        service_info = json.loads(question, strict=False)
        start_time = service_info.get("start_time")
        cell_name = service_info.get("cell_name")
        data = query_cell_log(start_time,cell_name)
        if data:
            plan_res = "1"
            plan_info =f"""\n### 意图识别
                        \n查询专业知识库中存储的历史处理信息,识别到的小区在历史处理信息中已存在  
                        """
            self.ces_stream_sender.send_msg(
                self.session_id, request_id, message=plan_info, begin_time=time.time()
            )
            time.sleep(1)
            plan_info = f"""\n### 业务保障方案
                                    """ + data["plan_info"]
            self.ces_stream_sender.send_msg(
                self.session_id, request_id, message=plan_info, begin_time=time.time()
            )
        else:
            plan_res = "0"
        return self.response_wrapper(LLmGeneration(content=plan_res))
