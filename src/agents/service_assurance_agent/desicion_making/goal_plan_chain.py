import json
import time
import uuid
from abc import ABC
from typing import Any, Dict, List, Optional

from an_contract.framework.agent_entity import AdditionalType, AgentResponseAdditional
from an_copilot.framework.chain.copilot_chain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LLmGeneration
from an_copilot.framework.logging import an_logger
from an_copilot.framework.utils.btree import (
    BehaviorHandler,
    BTree,
    BtreeContext,
    BtreeRegistry,
    NodeStatus,
)
from an_copilot.framework.utils.httpclient import HttpClient
from langchain_core.callbacks import CallbackManagerForChainRun
from src.core.ces_stream_sender import CesStreamSender
from src.config import settings
from src.agents.service_assurance_agent.desicion_making.goal_runner import GoalRunner
from src.core.service_query import save_cell_log


class GoalPlanChain(CopilotChain):
    service_btree_code: str = None
    http_client: HttpClient = None
    ces_stream_sender: CesStreamSender = None
    goal_runner: GoalRunner = None
    sm_context: dict  = None

    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        self.http_client = HttpClient(base_url=settings.config.ces.uri)
        self.ces_stream_sender = CesStreamSender(self.ces_stream)
        self.goal_runner: GoalRunner = None
        self.sm_context: dict = {}

    def get_name(self) -> str:
        return "Goal任务执行"

    def description(self) -> str:
        return "Goal任务执行"

    @property
    def input_keys(self) -> List[str]:
        return ["btree_code","question",]

    @property
    def output_keys(self) -> list[str]:
        return [self.output_key]

    def _call(
        self,
        inputs: Dict[str, Any],
        run_manager: Optional[CallbackManagerForChainRun] = None,
    ) -> Dict[str, str]:
        begin_time = time.time()
        session_and_request_json = json.loads(run_manager.tags[0])
        request_id = session_and_request_json.get("request_id")

        question = inputs.get("question")
        btree_code = inputs.get("btree_code")
        an_logger.info(f"GoalPlanChain 输入参数：inputs = {inputs}")
        an_logger.info(f"GoalPlanChain 输入参数：btree_code = {btree_code}")
        service_info = json.loads(question, strict=False)
        start_time = service_info.get("start_time")
        cell_name = service_info.get("cell_name")

        self.goal_runner = GoalRunner(btree_code=btree_code)
        inputs["session_id"] = self.session_id
        inputs["request_id"] = request_id
        inputs["cell_id"] = service_info.get("cell_id")
        inputs["cell_name"] = cell_name
        inputs["enodeb_id"] = service_info.get("enodeb_id")
        inputs["net_type"] = service_info.get("net_type")
        inputs["start_time"] = start_time
        self.sm_context.update(inputs)
        plan_info = self.goal_runner.run(inputs=self.sm_context)
        self.sm_context.update(self.goal_runner.context.get_data())
        if self.goal_runner.context:
            context_data = self.goal_runner.context.get_data()
            if context_data:
                btree_context_json = json.dumps(context_data)
                save_cell_log(start_time,cell_name, btree_code,btree_context_json,plan_info)
        return self.response_wrapper(LLmGeneration(content= plan_info))




