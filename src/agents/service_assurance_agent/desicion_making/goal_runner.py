import json
import time
from abc import ABC

from an_contract.framework.agent_entity import AdditionalType, AgentResponseAdditional
from an_copilot.framework.logging import an_logger
from an_copilot.framework.utils.btree import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    BtreeContext,
    BtreeReg<PERSON>ry,
    NodeStatus,
)
from an_copilot.framework.utils.httpclient import HttpClient

from src.config import settings
from src.core.ces_stream_sender import CesStreamSender


class GoalRunner:
    btree_code: str
    http_client: HttpClient = None
    context: BtreeContext = None
    ces_stream_sender: CesStreamSender = None

    def __init__(self, btree_code: str):
        self.btree_code = btree_code
        self.http_client = HttpClient(base_url=settings.config.ces.uri)
        self.ces_stream_sender = CesStreamSender(settings.ces_stream)

    def run(self, inputs: dict = None):
            begin_time = time.time()
            session_id = inputs.get("session_id")
            request_id = inputs.get("request_id")
            cell_name = inputs.get("cell_name")
            btree_dict = self.get_goal_retrieval(name=self.btree_code)
            btree_json = btree_dict["data"]
            an_logger.info(f"GoalRunner  {self.btree_code} -> {btree_json}")
            btree_additional = build_btree_additional(btree=btree_json)
            self.ces_stream_sender.begin(session_id, request_id, begin_time)
            self.ces_stream_sender.send_start(session_id, request_id, begin_time)
            self.ces_stream_sender.send_attachment(session_id, request_id, btree_additional, begin_time)

            # 初始化行为树
            self.context = BtreeContext()
            # 将input_context中的内容放入self.context
            if inputs:
                an_logger.info(f"inputs: {inputs}")
                for k, v in inputs.items():
                    self.context.put(k, v)
            btree = BTree.from_config(
                btree_json,
                BtreeRegistry.get_nodes(),
                context=self.context
            )
            an_logger.info(f"GoalRunner running btree: {self.btree_code}")
            # 执行行为树
            response = btree.run(show_status=True)
            node_result_dict = {}
            behaviour_function = {}
            # 构造执行结果，将行为树的执行路径通过additional发送给页面
            for node in response.get_nodes():
                node_info = {
                    "type": node.type,
                    "name": node.name,
                    "message": node.message,
                    "duration": node.duration,
                    "status": node.status.name,
                    "title": node.title,
                }
                behaviour_function[node.name] = node_info
                an_logger.info(f"GoalRunner  NODE: --> {node_info}")
                # 仅记录action节点的执行结果
                if node.type == "action":
                    if node.status.name == "SUCCESS":
                        run_result = True
                    elif node.status.name == "FAILURE":
                        run_result = False
                    else:
                        run_result = None
                    node_result_dict[node.name] = run_result
            # 发送行为树执行结果附加数据
            btree_function_additional = build_btree_function_additional(behaviour_function)
            self.ces_stream_sender.send_attachment(session_id, request_id, btree_function_additional, begin_time)
            self.ces_stream_sender.send_end(session_id, request_id, begin_time)
            self.ces_stream_sender.end(session_id, request_id, begin_time)
            nodes = [
                node for node in response.get_nodes()
                if node and node.status and  node.type == "action"
            ]
            if nodes:
                formatted_output = "\n- ".join(
                    f" {node.title}：{node.message if node.message else '无详细信息'}\n"
                    for node in nodes
                )
            else:
                formatted_output = "无"
            message = "\n### 任务执行分析 \n- "+ formatted_output
            an_logger.info(f"GoalRunner 任务执行分析: {message}")
            self.context.put("node_results", node_result_dict)
            self.ces_stream_sender.send_msg(session_id, request_id,message, begin_time)

            plan_output = []
            if node_result_dict.get('cell_app') == True and  self.context.get_data().get('cell_app_data', None)  :
                plan_output.append(self.context.get_data().get('cell_app_data'))
            if node_result_dict.get('cell_user') == True and  self.context.get_data().get('cell_user_data', None):
                plan_output.append(self.context.get_data().get('cell_user_data'))
            if node_result_dict.get('cell_check') == True and  self.context.get_data().get('cell_check_data', None):
                plan_output.append(self.context.get_data().get('cell_check_data'))
            if node_result_dict.get('video_highload') == True and  self.context.get_data().get('video_highload_data', None):
                plan_output.append(self.context.get_data().get('video_highload_data'))

            if len(plan_output) > 0:
                res_plan = f"\n### 业务保障方案  \n - " + "\n- ".join(plan_output)
            else:
                res_plan = f"\n### 业务保障方案  \n - 无"
            an_logger.info(f"GoalRunner 业务保障方案: {res_plan}")
            self.ces_stream_sender.send_msg(session_id, request_id, res_plan, begin_time)

            an_logger.info(f"GoalRunner self.context: {self.context.get_data()}")
            return res_plan

    def get_goal_retrieval(self, name: str) -> dict:
        """
        获取行为树的json
        """
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "from": "Y",
        }

        endpoint = f"/workflow/getNodesJson?code={name}&version=1.0.0"
        response = self.http_client.get(
            endpoint=endpoint,
            headers=headers,
        )
        response_json = response.json()
        response_json["data"] = json.loads(response_json["data"])
        return response_json


def build_btree_additional(
    btree: dict,
) -> AgentResponseAdditional:
    return AgentResponseAdditional(
        type=AdditionalType.JSON_DATA,
        value=json.dumps(
            {
                "type": "behaviour_tree",
                "value": btree,
            },
            ensure_ascii=False,
        ),
    )


def build_btree_function_additional(
    behaviour_function: dict,
) -> AgentResponseAdditional:
    return AgentResponseAdditional(
        type=AdditionalType.JSON_DATA,
        value=json.dumps(
            {
                "type": "behaviour_function",
                "value": behaviour_function,
            },
            ensure_ascii=False,
        ),
    )

class MyBehaviorHandler(BehaviorHandler, ABC):
    def pre(self, node: NodeStatus, context: BtreeContext):
        an_logger.info(node.name)

    def post(self, node: NodeStatus, context: BtreeContext):
        ces_stream_sender = CesStreamSender(settings.ces_stream)
        begin_time = time.time()
        if not context.has("behaviour_function"):
            context.put("behaviour_function", {})
        behaviour_function = context.get("behaviour_function")
        behaviour_function[node.name] = {
            "name": node.name,
            "message": node.message,
            "duration": node.duration,
            "status": node.status.name,
        }
        btree_function_additional = build_btree_function_additional(behaviour_function)
        ces_stream_sender.send_attachment(
            session_id=context.get("session_id"),
            request_id=context.get("request_id"),
            agent_additional=btree_function_additional,
            begin_time=begin_time,
        )
        an_logger.info(node.name)

