from datetime import datetime
import time
from typing import ClassVar, Dict, List, Optional

from an_contract.framework.agent_entity import AgentR<PERSON>ponse, HistoryMode
from an_copilot.framework.agent.copilot_agent import CopilotAgent
from an_copilot.framework.logging import an_logger
from langchain_core.callbacks import Callbacks
from langchain_core.prompts import PromptTemplate

from src.agents.cell_overload_agent.desicion_making.goal_plan_chain import GoalPlanChain
from src.agents.cell_overload_agent.desicion_making.prompts import *
from src.agents.cell_overload_agent.reflection.intent_recognition_chain import IntentRecognitionChain
from src.agents.cell_overload_agent.reflection.status_perception_chain import StatusPerception<PERSON>hain
from src.config import settings
from src.core.ces_stream_sender import CesStreamSender
from src.core.db_connector import DbConn


class CellOverloadAgent(CopilotAgent):
    name = "高负荷小区质差分析"
    version = "1.7.16"
    description = "高负荷小区质差分析"
    history_mode = HistoryMode.ALL.value
    skills: ClassVar[List[str]] = [
        "高负荷小区质差分析",
    ]

    def __init__(self, session_id: str):
        super().__init__(
            session_id=session_id,
            callbacks=settings.get_tracing_factory().getTracing(),
        )
        self.ces_stream_sender = CesStreamSender(settings.ces_stream)
        self.status_perception = StatusPerceptionChain.from_settings(
            session_id=self.session_id, settings=settings
        )
        self.intent_recognition = IntentRecognitionChain.from_settings(
            session_id=self.session_id, settings=settings
        )
        self.goal_plan = GoalPlanChain.from_settings(
            session_id=self.session_id, settings=settings
        )

    def _run(
        self,
        request_id: str,
        question: str,
        tags: Optional[List[str]],
        user_agent_config: Optional[dict] = None,
        history: List[List[str]] = None,
        previous_agent_history: List[Dict[str, List[List[str]]]] = None,
        history_messages: Optional[List[Dict]] = None,
        previous_agent_history_messages: Optional[List[Dict[str, List[Dict]]]] = None,
        agent_contexts=None,
        contexts: Optional[dict] = None,
        prompt_template: PromptTemplate = None,
        callbacks: Callbacks = None,
        reasoning: Optional[bool] = False,
    ) -> AgentResponse:
        an_logger.info(f"状态感知")
        status_perception_time = time.time()
        status_perception_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        status_perception_resp = self.status_perception.run(
            question=question, history=None, callbacks=callbacks, tags=tags
        )
        self.ces_stream_sender.send_msg(
            self.session_id,
            request_id,
            message=status_perception_resp,
            begin_time=status_perception_time)
        start_time = self.status_perception.sm_context.get("start_time").replace("-", "")
        enci = self.status_perception.sm_context.get("enci")

        an_logger.info(f"意图识别")
        intent_recognition = time.time()
        intent_recognition_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        inputs = {}
        inputs.update(self.status_perception.sm_context)
        intent_recognition_resp = self.intent_recognition.run(
            inputs, callbacks=callbacks, tags=tags
        )
        self.ces_stream_sender.send_msg(
            self.session_id,
            request_id,
            message=intent_recognition_resp,
            begin_time=intent_recognition)
        db_conn = None
        goal_plan_resp = None
        cell_name = self.intent_recognition.sm_context.get("cell_name")
        try:
            order_id = f"{start_time}{enci}"
            del_sql = f"DELETE FROM wireless_guarantee.tm_capacity_state WHERE order_id = '{order_id}'"
            sql = self.intent_recognition.sm_context.get("sql")
            db_conn = DbConn()
            db_conn.execute_sql_nodf(del_sql)
            db_conn.execute_sql_nodf(sql)

            sql1 = f"""INSERT INTO wireless_guarantee.tm_capacity_report
            (order_id, phase, report_content, report_time)
                VALUES('{order_id}', '开始高负荷优化，',"""
            sql = sql1 + f"'{status_perception_resp}', '{status_perception_date}')"
            del_sql = f"DELETE FROM wireless_guarantee.tm_capacity_report WHERE order_id = '{order_id}'"
            db_conn.execute_sql_nodf(del_sql)
            db_conn.execute_sql_nodf(sql)
            sql = sql1 + f"'{intent_recognition_resp}', '{intent_recognition_date}')"
            db_conn.execute_sql_nodf(sql)

            an_logger.info(f"任务规划")
            begin_time = time.time()
            goal_plan_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            inputs.update(self.intent_recognition.sm_context)
            goal_plan_resp = self.goal_plan.run(
                inputs, callbacks=callbacks, tags=tags
            )
            self.ces_stream_sender.send_msg(
                self.session_id,
                request_id,
                message=goal_plan_resp,
                begin_time=begin_time)
            sql = sql1 + f"'{goal_plan_resp}', '{goal_plan_date}')"
            db_conn.execute_sql_nodf(sql)

            # 获取各个节点返回数据
            begin_time = time.time()
            strategy_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            strategy_result = self.goal_plan.sm_context.get("scheme_suggest")
            bad_tool_names: str = self.goal_plan.sm_context.get("bad_tool_names")
            if bad_tool_names and len(bad_tool_names) > 0:
                strategy_message = CELL_OVERLOAD_STRATEGY_PROMPT.format(
                    bad_tool_names=self.goal_plan.sm_context.get("bad_tool_names"),
                    strategy_result=strategy_result)
                task_status = "优化成功"
            else:
                strategy_message = CELL_OVERLOAD_NO_STRATEGY_PROMPT
                task_status = "优化失败"
            self.ces_stream_sender.send_msg(
                self.session_id,
                request_id,
                message=strategy_message,
                begin_time=begin_time)
            sql = sql1 + f"'{strategy_message}', '{strategy_date}')"
            db_conn.execute_sql_nodf(sql)
            update_sql = f"""UPDATE wireless_guarantee.tm_capacity_state
                                        SET order_state = '{task_status}' 
                                        where order_id = '{order_id}'"""
            db_conn.execute_sql_nodf(update_sql)
        except Exception as e:
            an_logger.error(f"智能体执行失败：{e}")
        finally:
            if db_conn:
                db_conn.close()
        return AgentResponse(
            agent_name=self.name,
            agent_input=question,
            agent_output=goal_plan_resp
        )


CellOverloadAgent.register()
