import datetime
from typing import Any, Dict, List, Optional

from an_copilot.framework.chain.copilot_chain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from an_copilot.framework.logging import an_logger
from langchain_core.callbacks import CallbackManagerForChainRun

from src.agents.cell_overload_agent.desicion_making.prompts import CELL_OVERLOAD_PERCEPTION_PROMPT
from src.agents.cell_overload_agent.reflection.info_extraction_chain import recognize_enci, recognize_time
from src.config import settings
from src.core.ces_stream_sender import CesStreamSender


class StatusPerceptionChain(CopilotChain):
    """
    状态感知
    """
    ces_stream_sender: CesStreamSender = None
    sm_context: dict = None

    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        self.ces_stream_sender = CesStreamSender(settings.ces_stream)
        self.sm_context: dict = {}

    def get_name(self) -> str:
        return "状态感知"

    def description(self) -> str:
        return "状态感知"

    @property
    def input_keys(self) -> List[str]:
        return ["question", "history"]

    @property
    def output_keys(self) -> List[str]:
        return [self.output_key]

    def _call(
        self,
        inputs: Dict[str, Any],
        run_manager: Optional[CallbackManagerForChainRun] = None,
    ) -> Dict[str, str]:
        an_logger.info(f"{inputs} -> {inputs}")
        history = inputs.get('history')
        input_text = inputs.get('question')
        enci = recognize_enci(input_text)
        start_time = recognize_time(input_text)
        self.sm_context.update({"enci": enci, "start_time": start_time})
        formatted_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        return {
            "text": CELL_OVERLOAD_PERCEPTION_PROMPT.replace("**\n", "**").format(
                start_time=start_time,
                formatted_date=formatted_date)
        }
