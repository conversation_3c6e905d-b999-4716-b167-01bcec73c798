from datetime import datetime, <PERSON>elta
import json
import random
import time
from typing import Any, Dict, List, Optional

import requests
from an_copilot.framework.chain.copilot_chain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_core.callbacks import CallbackManagerForChainRun

from src.agents.cell_overload_agent.desicion_making.constants import *
from src.agents.cell_overload_agent.desicion_making.prompts import *
from src.config import settings
from src.core.ces_stream_sender import CesStreamSender


class IntentRecognitionChain(CopilotChain):
    """
    状态感知
    """
    ces_stream_sender: CesStreamSender = None
    sm_context: dict = None

    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        self.ces_stream_sender = CesStreamSender(settings.ces_stream)
        self.sm_context: dict = {}

    def get_name(self) -> str:
        return "状态感知"

    def description(self) -> str:
        return "状态感知"

    @property
    def input_keys(self) -> List[str]:
        return ["enci", "start_time"]

    @property
    def output_keys(self) -> List[str]:
        return [self.output_key]

    def _call(
        self,
        inputs: Dict[str, Any],
        run_manager: Optional[CallbackManagerForChainRun] = None,
    ) -> Dict[str, str]:
        begin_time = time.time()
        session_and_request_json = json.loads(run_manager.tags[0])
        request_id = session_and_request_json.get("request_id")
        enci = inputs.get("enci")
        end_time = inputs.get("start_time")
        seven_days_ago_time = datetime.strptime(end_time, "%Y-%m-%d") - timedelta(days=7)
        start_time = seven_days_ago_time.strftime("%Y-%m-%d")
        goal_info = self.select_goal(enci, end_time)
        btree_code = goal_info[0]
        goal_type = goal_info[1]
        task_plan_prompt = goal_info[2]
        cell_name = goal_info[3]
        self.sm_context.update({"btree_code": btree_code,
                                "cell_name": cell_name,
                                "goal_type": goal_type,
                                "task_plan_prompt": task_plan_prompt,
                                })
        message = (CELL_OVERLOAD_INTENT_PROMPT.replace("**\n", "**")
                   .format(cell_name=cell_name,
                           end_time=end_time,
                           start_time=start_time,
                           goal_type=goal_type,
                           network_type=goal_info[4],
                           site_type=goal_info[5],
                           bad_days=goal_info[6],
                           bad_num=goal_info[6].count("、") + 1,
                           ))
        return {
            "text": message
        }

    # goal选择
    def select_goal(self, enci=None, start_time=None):
        data = {
            "enci": enci,
            "start_time": f"{start_time} 00:00:00"
        }
        cell_info_endpoint = f"{SERVICE_URL}/api/capacity_agent/cellconfig"
        res = requests.post(url=f"{cell_info_endpoint}", json=data, headers=HEADERS)
        cell_info = res.json().get("output_data")
        network_type = cell_info.get("network_type")
        site_type = cell_info.get("site_type")
        cell_name = cell_info.get("cell_name")
        province_id = cell_info.get("province_id")
        province = cell_info.get("province")
        city_id = cell_info.get("city_id")
        city = cell_info.get("city")
        county_id = cell_info.get("county_id")
        county = cell_info.get("county")
        order_id = f"{start_time}{enci}".replace("-", "")
        sql = f"""INSERT INTO wireless_guarantee.tm_capacity_state (start_time, province_id, 
        province, city_id, city, county_id, county, enci, cell_name, order_id, order_state, 
        recognition_type) VALUES('{start_time}', '{province_id}', '{province}', '{city_id}',
         '{city}', '{county_id}', '{county}', '{enci}', '{cell_name}', '{order_id}',
          '流程中断', '高负荷')"""
        self.sm_context.update({"sql": sql})
        sudden_overload_endpoint = f"{SERVICE_URL}/api/capacity_agent/recognition"
        bad_days = '2025-01-13'
        is_sudden = random.random() < 1
        if is_sudden:
            return ('highload-scheme-sudden',
                    '突发高负荷',
                    CELL_OVERLOAD_PLAN_SUDDEN_PROMPT.replace("**\n", "**"),
                    cell_name,
                    network_type,
                    site_type,
                    bad_days
                    )
        else:
            if site_type == '宏站':
                if network_type == '5G':
                    return ('5Gmacro-longterm-highload-scheme',
                            '长期高负荷的5G宏站小区',
                            CELL_OVERLOAD_PLAN_LONGTIME_5G_MACRO_PROMPT.replace("**\n", "**"),
                            cell_name,
                            network_type,
                            site_type,
                            bad_days
                            )
                else:
                    return ('4Gmacro-longterm-highload-scheme',
                            '长期高负荷的4G宏站小区',
                            CELL_OVERLOAD_PLAN_LONGTIME_4G_MACRO_PROMPT.replace("**\n", "**"),
                            cell_name,
                            network_type,
                            site_type,
                            bad_days
                            )
            elif site_type == '室分':
                if network_type == '5G':
                    return ('5Gindoor-longterm-highload-scheme',
                            '长期高负荷的5G室分小区',
                            CELL_OVERLOAD_PLAN_LONGTIME_5G_MICRO_PROMPT.replace("**\n", "**"),
                            cell_name,
                            network_type,
                            site_type,
                            bad_days
                            )
                else:
                    return ('4Gindoor-longterm-highload-scheme',
                            '长期高负荷的4G室分小区',
                            CELL_OVERLOAD_PLAN_LONGTIME_4G_MICRO_PROMPT.replace("**\n", "**"),
                            cell_name,
                            network_type,
                            site_type,
                            bad_days
                            )
