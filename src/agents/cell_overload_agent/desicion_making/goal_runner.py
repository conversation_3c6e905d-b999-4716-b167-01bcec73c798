import json
import time
from abc import ABC

from an_contract.framework.agent_entity import AdditionalType, AgentResponseAdditional
from an_copilot.framework.logging import an_logger
from an_copilot.framework.utils.btree import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>tree<PERSON>ontext,
    <PERSON>treeRegistry,
    NodeStatus, behaviour, BehaviorResponse,
)
from an_copilot.framework.utils.httpclient import HttpClient
from py_trees.common import Status

from src.agents.cell_overload_agent.desicion_making.constants import HEADERS
from src.agents.cell_overload_agent.desicion_making.prompts import CELL_OVERLOAD_STRATEGY_PROMPT, \
    CELL_OVERLOAD_EXEC_PROMPT
from src.config import settings
from src.core.ces_stream_sender import CesStreamSender


class GoalRunner:
    btree_code: str
    http_client: HttpClient = None
    context: BtreeContext = None
    ces_stream_sender: CesStreamSender = None

    def __init__(self, btree_code: str):
        self.btree_code = btree_code
        self.http_client = HttpClient(base_url=settings.config.ces.uri)
        self.ces_stream_sender = CesStreamSender(settings.ces_stream)

    def run(self, inputs: dict = None):
        begin_time = time.time()
        session_id = inputs.get("session_id")
        request_id = inputs.get("request_id")
        cell_name = inputs.get("cell_name")
        btree_dict = self.get_goal_retrieval(name=self.btree_code)
        btree_json = btree_dict["data"]
        an_logger.info(f"GoalRunner  {self.btree_code} -> {btree_json}")
        btree_additional = build_btree_additional(btree=btree_json)
        self.ces_stream_sender.begin(session_id, request_id, begin_time)
        self.ces_stream_sender.send_start(session_id, request_id, begin_time)
        self.ces_stream_sender.send_attachment(session_id, request_id, btree_additional, begin_time)
        self.context = BtreeContext()
        # 将input_context中的内容放入self.context
        if inputs:
            an_logger.info(f"inputs: {inputs}")
            for k, v in inputs.items():
                self.context.put(k, v)
        btree = BTree.from_config(
            btree_json,
            BtreeRegistry.get_nodes(),
            context=self.context
        )
        an_logger.info(f"GoalRunner running btree: {self.btree_code}")
        response = btree.run(show_status=True)
        node_result_dict = {}
        behaviour_function = {}
        for node in response.get_nodes():
            node_info = {
                "type": node.type,
                "name": node.name,
                "message": node.message,
                "duration": node.duration,
                "status": node.status.name,
                "title": node.title,
            }
            behaviour_function[node.name] = node_info
            an_logger.info(f"GoalRunner  NODE: --> {node_info}")
            # 仅记录action节点的执行结果
            if node.type == "action":
                if node.status.name == "SUCCESS":
                    run_result = True
                elif node.status.name == "FAILURE":
                    run_result = False
                else:
                    run_result = None
                node_result_dict[node.name] = run_result
        # 发送行为树执行结果附加数据
        btree_function_additional = build_btree_function_additional(behaviour_function)
        self.ces_stream_sender.send_attachment(session_id, request_id, btree_function_additional, begin_time)
        self.ces_stream_sender.send_end(session_id, request_id, begin_time)
        self.ces_stream_sender.end(session_id, request_id, begin_time)

        nodes = []
        bad_node_titles = []
        for node in response.get_nodes():
            if node.type == "action" and node.title != "方案总结":
                nodes.append(node)
                if node.status != Status.SUCCESS:
                    bad_node_titles.append(node.title)
        bad_tool_names = '、'.join(str(i) for i in bad_node_titles)
        self.context.put("bad_tool_names", bad_tool_names)
        formatted_output = "\n".join(
            f" {node.title}：{node.message if node.message else '无详细信息'}\n"
            for node in nodes
        )
        tool_names = "、".join(
            node.title
            for node in nodes
        )
        exec_resp = CELL_OVERLOAD_EXEC_PROMPT.replace("**\n", "**").format(
            tool_num=len(nodes),
            tool_names=tool_names,
            exec_result=formatted_output
        )
        return exec_resp.replace("**\n", "**")

    def get_goal_retrieval(self, name: str) -> dict:
        """
        获取行为树的json
        """
        endpoint = f"/workflow/getNodesJson?code={name}&version=1.0.0"
        response = self.http_client.get(
            endpoint=endpoint,
            headers=HEADERS,
        )
        response_json = response.json()
        response_json["data"] = json.loads(response_json["data"])
        return response_json

    @behaviour
    def cell_overload_conclusion0(self: BtreeContext) -> BehaviorResponse:
        # weak_grid_rate = self.get("weak_grid_rate")
        # if weak_grid_rate >= 0.3:
        #     return BehaviorResponse(status=Status.FAILURE, data="弱覆盖栅格占比大于等于30%")
        # else:
        return BehaviorResponse(status=Status.SUCCESS, data="未生成方案总结")

    @behaviour
    def cell_overload_conclusion(self: BtreeContext) -> BehaviorResponse:
        return BehaviorResponse(status=Status.SUCCESS, data="生成方案总结")


def build_btree_additional(
    btree: dict,
) -> AgentResponseAdditional:
    return AgentResponseAdditional(
        type=AdditionalType.JSON_DATA,
        value=json.dumps(
            {
                "type": "behaviour_tree",
                "value": btree,
            },
            ensure_ascii=False,
        ),
    )


def build_btree_function_additional(
    behaviour_function: dict,
) -> AgentResponseAdditional:
    return AgentResponseAdditional(
        type=AdditionalType.JSON_DATA,
        value=json.dumps(
            {
                "type": "behaviour_function",
                "value": behaviour_function,
            },
            ensure_ascii=False,
        ),
    )


class MyBehaviorHandler(BehaviorHandler, ABC):
    def pre(self, node: NodeStatus, context: BtreeContext):
        an_logger.info(node.name)

    def post(self, node: NodeStatus, context: BtreeContext):
        ces_stream_sender = CesStreamSender(settings.ces_stream)
        begin_time = time.time()
        if not context.has("behaviour_function"):
            context.put("behaviour_function", {})
        behaviour_function = context.get("behaviour_function")
        behaviour_function[node.name] = {
            "name": node.name,
            "message": node.message,
            "duration": node.duration,
            "status": node.status.name,
        }
        btree_function_additional = build_btree_function_additional(behaviour_function)
        ces_stream_sender.send_attachment(
            session_id=context.get("session_id"),
            request_id=context.get("request_id"),
            agent_additional=btree_function_additional,
            begin_time=begin_time,
        )
        an_logger.info(node.name)
