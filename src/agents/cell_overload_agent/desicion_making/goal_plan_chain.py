import json
import time
from datetime import datetime
from typing import Any, Dict, List, Optional

from an_copilot.framework.chain.copilot_chain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from an_copilot.framework.logging import an_logger
from an_copilot.framework.utils.httpclient import HttpClient
from langchain_core.callbacks import CallbackManagerFor<PERSON>hainRun

from src.agents.cell_overload_agent.desicion_making.prompts import CELL_OVERLOAD_GOAL_MANAGER_PROMPT
from src.agents.cell_overload_agent.desicion_making.goal_runner import GoalRunner
from src.config import settings
from src.core.ces_stream_sender import CesStreamSender
from src.core.db_connector import DbConn


class GoalPlanChain(CopilotChain):
    request_id: str = None
    http_client: HttpClient = None
    ces_stream_sender: CesStreamSender = None
    goal_runner: GoalRunner = None
    sm_context: dict = None

    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        self.http_client = HttpClient(base_url=settings.config.ces.uri)
        self.ces_stream_sender = CesStreamSender(self.ces_stream)
        self.goal_runner: GoalRunner = None
        self.sm_context: dict = {}

    def get_name(self) -> str:
        return "Goal任务执行"

    def description(self) -> str:
        return "Goal任务执行"

    @property
    def input_keys(self) -> List[str]:
        return ["enci", "start_time"]

    @property
    def output_keys(self) -> list[str]:
        return [self.output_key]

    def _call(
        self,
        inputs: Dict[str, Any],
        run_manager: Optional[CallbackManagerForChainRun] = None,
    ) -> Dict[str, str]:
        begin_time = time.time()
        goal_manager_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        session_and_request_json = json.loads(run_manager.tags[0])
        request_id = session_and_request_json.get("request_id")
        enci = inputs.get("enci")
        start_time = inputs.get("start_time")
        btree_code = inputs.get("btree_code")
        goal_type = inputs.get("goal_type")
        goal_manager_message = CELL_OVERLOAD_GOAL_MANAGER_PROMPT.format(goal_type=goal_type).replace("**\n", "**")
        self.ces_stream_sender.send_msg(
            self.session_id,
            self.request_id,
            goal_manager_message,
            begin_time)
        time.sleep(1)

        task_plan_prompt = inputs.get("task_plan_prompt").replace("**\n", "**")
        self.ces_stream_sender.send_msg(
            self.session_id,
            self.request_id,
            task_plan_prompt,
            begin_time)
        self.goal_runner = GoalRunner(btree_code=btree_code)
        inputs["session_id"] = self.session_id
        inputs["request_id"] = request_id
        inputs["start_time"] = f"{start_time} 00:00:00"
        self.sm_context.update(inputs)
        db_conn = None
        try:
            order_id = f"{start_time}{enci}".replace("-", "")
            cell_name = inputs.get("cell_name")
            sql1 = f"""INSERT INTO wireless_guarantee.tm_capacity_report
                        (order_id, phase, report_content, report_time)
                        VALUES('{order_id}', '开始高负荷优化，',"""
            sql = sql1 + f"'{goal_manager_message}', '{goal_manager_date}')"
            db_conn = DbConn()
            db_conn.execute_sql_nodf(sql)
            sql = sql1 + f"'{task_plan_prompt}', '{goal_manager_date}')"
            db_conn.execute_sql_nodf(sql)
        except Exception as e:
            an_logger.error(f"执行数据库操作失败: {e}")
        finally:
            db_conn.close()
        goal_runner_resp = self.goal_runner.run(inputs=self.sm_context)
        self.sm_context.update(self.goal_runner.context.get_data())
        return {
            "text": goal_runner_resp,
            "value": "success"
        }
