CELL_OVERLOAD_PERCEPTION_PROMPT = \
    """\n#### **状态感知** 
        \n{formatted_date}扫描质差识别结果业务表 
        \n获取数据日期为{start_time}的质差小区一条 
        \n启动4/5G性能质差优化智能体"""

CELL_OVERLOAD_GOAL_MANAGER_PROMPT = \
    """\n#### **目标管理** \n根据意图理解，该质差小区采用{goal_type}性能优化流程。"""

CELL_OVERLOAD_INTENT_PROMPT = \
    """\n#### **意图识别** 
        \n查询获取质差小区信息为：{end_time}、{cell_name}、高负荷质差，该小区为高负荷质差小区，现需要对该小区进行性能优化方案生成。
        \n根据4/5G性能优化知识库，需要：判断该质差小区是突发高负荷还是长期高负荷？确定质差小区的网络类型和站型。
        \n查询历史记录（{bad_days}），在包含当天的7天连续自然日（{start_time}至{end_time}）内高负荷次数≥{bad_num}次（≥2次阈值），符合“{goal_type}”特征。
        \n查询网络类型（{network_type}）和站型（{site_type}），归类为{network_type}{site_type}。
        \n{cell_name}小区为{goal_type}{network_type}{site_type}小区，需要{goal_type}{network_type}{site_type}小区性能优化方案生成。
    """

CELL_OVERLOAD_PLAN_LONGTIME_5G_MICRO_PROMPT = \
    """\n#### **任务规划** 
        \n**基于长期高负荷的5G室分小区的提示词，实施根因分析方案**
        \n1.本小区告警：查询小区在问题存在的时段，是否存在影响小区存在的问题状态的告警。
        \n2.带宽配置问题：核查小区带宽是否开启小区频点可支持的最大带宽。
        \n3.终端/用户问题：核查小区在高负荷时段是否存在高流量用户导致小区高负荷。
        \n4.参数合理性核查：根据问题小区存在的问题类型，结合小区同覆盖的同类邻区小区参数设置情况，结合不同问题类型的参数调整建议，核查出问题小区设置异常的参数，并输出异常参数调整方案。
        \n5.功能开关：核查小区存在影响不同业务的功能开关的开启情况。
        \n6.邻区告警：查询邻区在高负荷时段是否存在影响告警导致邻区负荷能力降低，导致高负荷小区的负荷升高。
        \n7.邻区问题：查询小区是否存在异常问题的邻区。"""

CELL_OVERLOAD_PLAN_LONGTIME_5G_MACRO_PROMPT = \
    """\n#### **任务规划** 
        \n**基于长期高负荷的5G宏站小区的提示词，实施根因分析方案**
        \n1.本小区告警：查询小区在问题存在的时段，是否存在影响小区存在的问题状态的告警。
        \n2.带宽配置问题：核查小区带宽是否开启小区频点可支持的最大带宽。
        \n3.终端/用户问题：核查小区在高负荷时段是否存在高流量用户导致小区高负荷。
        \n4.共站共覆盖不均衡：核查宏站高负荷小区与共站共覆盖小区间的负荷情况，并输出根因定位及均衡方案。
        \n5.参数合理性核查：根据问题小区存在的问题类型，结合小区同覆盖的同类邻区小区参数设置情况，结合不同问题类型的参数调整建议，核查出问题小区设置异常的参数，并输出异常参数调整方案。
        \n6.功能开关：核查小区存在影响不同业务的功能开关的开启情况。
        \n7.邻区告警：查询邻区在高负荷时段是否存在影响告警导致邻区负荷能力降低，导致高负荷小区的负荷升高。
        \n8.邻区问题：查询小区是否存在异常问题的邻区。
        \n9.下倾角问题：查询小区是否存站高和下倾角异常的情况。
        \n10.共建共享问题：查询小区周边300米内是否存在未共享的小区，该能力仅限于4G电联运营商环境。
        \n11.站间距问题：查询小区是否存站间距过大的情况。"""

CELL_OVERLOAD_PLAN_LONGTIME_4G_MICRO_PROMPT = \
    """\n#### **任务规划** 
        \n**基于长期高负荷的4G室分小区的提示词，实施根因分析方案**
        \n1.本小区告警：查询小区在问题存在的时段，是否存在影响小区存在的问题状态的告警。
        \n2.带宽配置问题：核查小区带宽是否开启小区频点可支持的最大带。
        \n3.终端/用户问题：核查小区在高负荷时段是否存在高流量用户导致小区高负荷。
        \n4.参数合理性核查：根据问题小区存在的问题类型，结合小区同覆盖的同类邻区小区参数设置情况，结合不同问题类型的参数调整建议，核查出问题小区设置异常的参数，并输出异常参数调整方案。
        \n5.功能开关：核查小区存在影响不同业务的功能开关的开启情况。
        \n6.邻区告警：查询邻区在高负荷时段是否存在影响告警导致邻区负荷能力降低，导致高负荷小区的负荷升高。
        \n7.邻区问题：查询小区是否存在异常问题的邻区。
        \n8.站间距问题：查询小区是否存站间距过大的情况。"""

CELL_OVERLOAD_PLAN_LONGTIME_4G_MACRO_PROMPT = \
    """\n#### **任务规划** 
        \n**基于长期高负荷的4G宏站小区的提示词，实施根因分析方案**
        \n1.本小区告警：查询小区在问题存在的时段，是否存在影响小区存在的问题状态的告警
        \n2.带宽配置问题：核查小区带宽是否开启小区频点可支持的最大带宽
        \n3.终端/用户问题：核查小区在高负荷时段是否存在TOP3用户的业务量超过小区总业务的80%。
        \n4.4G共站共覆盖不均衡（宏站）：核查4G宏站高负荷小区与共站共覆盖小区间的负荷情况，并输出根因定位及均衡方案。
        \n5.参数合理性核查：根据问题小区存在的问题类型，结合小区同覆盖的同类邻区小区参数设置情况，结合不同问题类型的参数调整建议，核查出问题小区设置异常的参数，并输出异常参数调整方案。
        \n6.功能开关：核查小区存在影响不同业务的功能开关的开启情况。
        \n7.邻区告警：查询邻区在高负荷时段是否存在影响告警导致邻区负荷能力降低，导致高负荷小区的负荷升高。
        \n8.邻区问题：查询小区是否存在异常问题的邻区
        \n9.下倾角问题：查询小区是否存站高和下倾角异常的情况。
        \n10.共建共享问题：查询小区周边300米内是否存在未共享的小区，该能力仅限于4G电联运营商环境
        \n11.站间距问题：查询小区是否存站间距过大的情况。"""

CELL_OVERLOAD_PLAN_SUDDEN_PROMPT = \
    """\n#### **任务规划** 
        \n**基于突发高负荷的提示词，实施根因分析方案**
        \n1.本小区告警：查询小区在问题存在的时段，是否存在影响小区存在的问题状态的告警
        \n2.终端/用户问题：核查小区在高负荷时段是否存在TOP3用户的业务量超过小区总业务的80%。
        \n3.邻区告警：查询邻区在高负荷时段是否存在影响告警导致邻区负荷能力降低，导致高负荷小区的负荷升高。
    """

CELL_OVERLOAD_EXEC_PROMPT = \
    """\n#### **任务执行** 
    \n**根因分析智能推理**
    \n分析过程：针对突发高负荷小区，根据4/5G性能优化知识库，智能体需要核查以下{tool_num}个工具：{tool_names}。各项核查存在异常的情况如下：
    \n{exec_result}
    """

CELL_OVERLOAD_STRATEGY_PROMPT = \
    """\n#### **方案总结** 
    \n**优化方案推理输出**
    \n根据根因分析结果，根据4/5G性能优化知识库，智能体针对{bad_tool_names}制定了优化方案，各项推荐优化方案如下：
    \n{strategy_result}
    """

CELL_OVERLOAD_NO_STRATEGY_PROMPT = \
    """\n#### **方案总结** 
    \n**优化方案推理输出**
    \n根据根因分析结果，根据4/5G性能优化知识库，智能体针未发现高负荷小区存在异常问题，无法输出优化方案。
    """
