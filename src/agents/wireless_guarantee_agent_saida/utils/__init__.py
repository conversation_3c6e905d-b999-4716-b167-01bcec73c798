"""
无线保障智能体SAIDA架构工具模块

本模块包含无线保障智能体的工具类和辅助组件，主要是对现有组件的封装和适配：
- 状态管理工具：提供状态转换和验证功能
- 数据库操作工具：封装数据库操作和事务管理
- 其他辅助工具：提供通用的工具函数

这些工具类不重复实现已有功能，而是对现有组件进行封装和适配，
提供更便于SAIDA架构使用的接口。

作者: RIPER-5 重构团队
日期: 2025-07-24
版本: 1.0.0
"""

from .state_manager import WGStateManager
from .db_operations import WGDatabaseOperations

__all__ = [
    "WGStateManager",
    "WGDatabaseOperations",
]

# 版本信息
__version__ = "1.0.0"
__description__ = "无线保障智能体SAIDA架构工具模块"
