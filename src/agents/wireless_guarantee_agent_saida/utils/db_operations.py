"""
无线保障智能体数据库操作工具

本模块提供数据库操作工具，封装数据库连接、事务管理和常用操作。
主要是对现有DbConn组件的封装和适配，不重复实现已有功能。

主要功能：
1. 数据库连接管理和事务处理
2. 常用数据库操作的封装
3. 批量操作和性能优化
4. 错误处理和重试机制

作者: RIPER-5 重构团队
日期: 2025-07-24
版本: 1.0.0
"""

from typing import Dict, List, Optional, Any, Tuple
from contextlib import contextmanager

from an_copilot.framework.logging import an_logger

from src.core.db_connector import DbConn
from src.agents.wireless_guarantee_agent_saida.wg_saida_define import WG_DATABASE_TABLES


class WGDatabaseOperations:
    """
    无线保障智能体数据库操作工具
    
    封装数据库连接、事务管理和常用操作，提供便于SAIDA架构使用的接口。
    """
    
    # 数据库表名配置（从预定义配置获取）
    TABLES = WG_DATABASE_TABLES
    
    def __init__(self):
        """初始化数据库操作工具"""
        self._db_conn: Optional[DbConn] = None
    
    @contextmanager
    def get_connection(self):
        """
        获取数据库连接的上下文管理器
        
        使用示例:
            with db_ops.get_connection() as db:
                result = db.execute_sql_withdf(...)
        """
        db = None
        try:
            db = DbConn()
            self._db_conn = db
            yield db
        except Exception as e:
            an_logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if db:
                db.close()
                self._db_conn = None
    
    def execute_with_retry(self, operation_func, max_retries: int = 3, *args, **kwargs):
        """
        带重试机制的数据库操作执行
        
        Args:
            operation_func: 要执行的操作函数
            max_retries: 最大重试次数
            *args, **kwargs: 传递给操作函数的参数
            
        Returns:
            操作结果
            
        Raises:
            Exception: 重试次数用尽后仍失败时抛出最后一次异常
        """
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                return operation_func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt < max_retries:
                    an_logger.warning(f"数据库操作失败，第{attempt + 1}次重试: {e}")
                else:
                    an_logger.error(f"数据库操作重试{max_retries}次后仍失败: {e}")
        
        raise last_exception
    
    def insert_alarm_info(self, alarm_data: Dict[str, Any]) -> bool:
        """
        插入告警信息
        
        Args:
            alarm_data: 告警数据字典
            
        Returns:
            bool: 插入是否成功
        """
        try:
            with self.get_connection() as db:
                # 构建INSERT语句
                table_name = self.TABLES["alarm_table"]
                columns = ', '.join(alarm_data.keys())
                placeholders = ', '.join(['%s'] * len(alarm_data))
                values = tuple(alarm_data.values())
                
                sql = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
                db.execute_sql_withdf(values, sql)
                
                an_logger.info(f"告警信息插入成功: {alarm_data.get('serialno', 'unknown')}")
                return True
                
        except Exception as e:
            an_logger.error(f"告警信息插入失败: {e}")
            return False
    
    def update_alarm_info(self, serialno: str, update_data: Dict[str, Any]) -> bool:
        """
        更新告警信息
        
        Args:
            serialno: 告警流水号
            update_data: 更新数据字典
            
        Returns:
            bool: 更新是否成功
        """
        try:
            with self.get_connection() as db:
                table_name = self.TABLES["alarm_table"]
                set_clause = ', '.join([f"{key} = %s" for key in update_data.keys()])
                values = list(update_data.values()) + [serialno]
                
                sql = f"UPDATE {table_name} SET {set_clause} WHERE serialno = %s"
                db.execute_sql_withdf(values, sql)
                
                an_logger.info(f"告警信息更新成功: {serialno}")
                return True
                
        except Exception as e:
            an_logger.error(f"告警信息更新失败: {e}")
            return False
    
    def insert_state_history(self, state_data: Dict[str, Any]) -> bool:
        """
        插入状态历史记录
        
        Args:
            state_data: 状态数据字典
            
        Returns:
            bool: 插入是否成功
        """
        try:
            with self.get_connection() as db:
                table_name = self.TABLES["state_history_table"]
                columns = ', '.join(state_data.keys())
                placeholders = ', '.join(['%s'] * len(state_data))
                values = tuple(state_data.values())
                
                sql = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
                db.execute_sql_withdf(values, sql)
                
                an_logger.info(f"状态历史插入成功: {state_data.get('serialno', 'unknown')}")
                return True
                
        except Exception as e:
            an_logger.error(f"状态历史插入失败: {e}")
            return False
    
    def insert_evaluate_history(self, evaluate_data: Dict[str, Any]) -> bool:
        """
        插入评估历史记录
        
        Args:
            evaluate_data: 评估数据字典
            
        Returns:
            bool: 插入是否成功
        """
        try:
            with self.get_connection() as db:
                table_name = self.TABLES["evaluate_history_table"]
                columns = ', '.join(evaluate_data.keys())
                placeholders = ', '.join(['%s'] * len(evaluate_data))
                values = tuple(evaluate_data.values())
                
                sql = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
                db.execute_sql_withdf(values, sql)
                
                an_logger.info(f"评估历史插入成功: {evaluate_data.get('serialno', 'unknown')}")
                return True
                
        except Exception as e:
            an_logger.error(f"评估历史插入失败: {e}")
            return False
    
    def query_alarm_by_serialno(self, serialno: str) -> Optional[Dict[str, Any]]:
        """
        根据流水号查询告警信息
        
        Args:
            serialno: 告警流水号
            
        Returns:
            Optional[Dict[str, Any]]: 告警信息字典，未找到时返回None
        """
        try:
            with self.get_connection() as db:
                table_name = self.TABLES["alarm_table"]
                sql = f"SELECT * FROM {table_name} WHERE serialno = %s"
                result = db.execute_sql_withdf([serialno], sql)
                
                if result and len(result) > 0:
                    # 将DataFrame转换为字典
                    alarm_info = result.iloc[0].to_dict()
                    an_logger.info(f"告警信息查询成功: {serialno}")
                    return alarm_info
                else:
                    an_logger.warning(f"未找到告警信息: {serialno}")
                    return None
                    
        except Exception as e:
            an_logger.error(f"告警信息查询失败: {e}")
            return None
    
    def query_state_history(self, serialno: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        查询状态历史记录
        
        Args:
            serialno: 告警流水号
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 状态历史记录列表
        """
        try:
            with self.get_connection() as db:
                table_name = self.TABLES["state_history_table"]
                sql = f"""
                    SELECT * FROM {table_name} 
                    WHERE serialno = %s 
                    ORDER BY create_time DESC 
                    LIMIT %s
                """
                result = db.execute_sql_withdf([serialno, limit], sql)
                
                if result is not None and len(result) > 0:
                    # 将DataFrame转换为字典列表
                    history_list = result.to_dict('records')
                    an_logger.info(f"状态历史查询成功: {serialno}, 记录数: {len(history_list)}")
                    return history_list
                else:
                    an_logger.info(f"未找到状态历史: {serialno}")
                    return []
                    
        except Exception as e:
            an_logger.error(f"状态历史查询失败: {e}")
            return []
    
    def batch_insert(self, table_key: str, data_list: List[Dict[str, Any]]) -> bool:
        """
        批量插入数据
        
        Args:
            table_key: 表名键（在TABLES中的键）
            data_list: 数据字典列表
            
        Returns:
            bool: 批量插入是否成功
        """
        if not data_list:
            return True
        
        try:
            with self.get_connection() as db:
                table_name = self.TABLES[table_key]
                
                # 使用第一条记录的键作为列名
                columns = list(data_list[0].keys())
                columns_str = ', '.join(columns)
                placeholders = ', '.join(['%s'] * len(columns))
                
                sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
                
                # 准备批量数据
                values_list = [tuple(data[col] for col in columns) for data in data_list]
                
                # 执行批量插入
                for values in values_list:
                    db.execute_sql_withdf(values, sql)
                
                an_logger.info(f"批量插入成功: {table_name}, 记录数: {len(data_list)}")
                return True
                
        except Exception as e:
            an_logger.error(f"批量插入失败: {e}")
            return False
    
    def get_table_info(self) -> Dict[str, str]:
        """获取表配置信息"""
        return dict(self.TABLES)
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            with self.get_connection() as db:
                # 执行简单查询测试连接
                result = db.execute_sql_withdf([], "SELECT 1 as test")
                return result is not None
        except Exception as e:
            an_logger.error(f"数据库连接测试失败: {e}")
            return False
