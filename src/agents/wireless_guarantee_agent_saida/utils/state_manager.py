"""
无线保障智能体状态管理工具

本模块提供状态管理工具，封装状态转换、验证和持久化功能。
主要是对现有状态机组件的封装和适配，不重复实现已有功能。

主要功能：
1. 状态转换验证和管理
2. 状态历史记录和查询
3. 状态持久化操作
4. 状态兼容性检查

作者: RIPER-5 重构团队
日期: 2025-07-24
版本: 1.0.0
"""

from typing import Dict, List, Optional, Any
from enum import StrEnum

from an_copilot.framework.logging import an_logger

from src.agents.wireless_guarantee_agent_saida.wg_saida_model import (
    WGState,
    WGStateEnum,
)


class WGStateManager:
    """
    无线保障智能体状态管理工具
    
    提供状态转换、验证和持久化功能，封装原有状态机的核心逻辑。
    """
    
    # 状态转换规则（基于原状态机逻辑）
    STATE_TRANSITIONS = {
        WGStateEnum.RECOGNIZING: [
            WGStateEnum.GENERATING,    # 识别完成 → 生成方案
            WGStateEnum.RESETTING,     # 清除告警 → 复位
            WGStateEnum.INTERRUPTED,   # 异常中断
        ],
        WGStateEnum.GENERATING: [
            WGStateEnum.APPROVING,     # 方案生成完成 → 审核
            WGStateEnum.INTERRUPTED,   # 异常中断
        ],
        WGStateEnum.APPROVING: [
            WGStateEnum.EXECUTING,     # 审批通过 → 执行
            WGStateEnum.ARCHIVED,      # 审批不通过 → 归档
            WGStateEnum.INTERRUPTED,   # 异常中断
        ],
        WGStateEnum.EXECUTING: [
            WGStateEnum.EVALUATING,    # 执行成功 → 评估
            WGStateEnum.INTERRUPTED,   # 执行失败 → 中断
        ],
        WGStateEnum.EVALUATING: [
            WGStateEnum.EVALUATED,     # 评估成功 → 完成
            WGStateEnum.RESETTING,     # 评估失败 → 复位
            WGStateEnum.INTERRUPTED,   # 异常中断
        ],
        WGStateEnum.EVALUATED: [
            WGStateEnum.RESETTING,     # 需要复位
            WGStateEnum.ARCHIVED,      # 归档
        ],
        WGStateEnum.RESETTING: [
            WGStateEnum.ARCHIVED,      # 复位成功 → 归档
            WGStateEnum.INTERRUPTED,   # 复位失败 → 中断
        ],
        WGStateEnum.INTERRUPTED: [
            WGStateEnum.RECOGNIZING,   # 重新开始
            WGStateEnum.ARCHIVED,      # 归档
        ],
        WGStateEnum.ARCHIVED: [],      # 终态，无后续转换
    }
    
    # 状态描述映射
    STATE_DESCRIPTIONS = {
        WGStateEnum.RECOGNIZING: "问题识别中",
        WGStateEnum.GENERATING: "方案生成中",
        WGStateEnum.APPROVING: "方案审核中",
        WGStateEnum.EXECUTING: "方案执行中",
        WGStateEnum.EVALUATING: "效果评估中",
        WGStateEnum.EVALUATED: "已评估通过",
        WGStateEnum.RESETTING: "方案复位中",
        WGStateEnum.INTERRUPTED: "已中断",
        WGStateEnum.ARCHIVED: "已归档",
    }
    
    @classmethod
    def validate_state_transition(cls, from_state: WGStateEnum, to_state: WGStateEnum) -> bool:
        """
        验证状态转换是否合法
        
        Args:
            from_state: 源状态
            to_state: 目标状态
            
        Returns:
            bool: 转换是否合法
        """
        if from_state not in cls.STATE_TRANSITIONS:
            an_logger.warning(f"未知的源状态: {from_state}")
            return False
        
        allowed_states = cls.STATE_TRANSITIONS[from_state]
        is_valid = to_state in allowed_states
        
        if not is_valid:
            an_logger.warning(
                f"非法状态转换: {from_state} → {to_state}, "
                f"允许的转换: {allowed_states}"
            )
        
        return is_valid
    
    @classmethod
    def get_next_states(cls, current_state: WGStateEnum) -> List[WGStateEnum]:
        """
        获取当前状态的所有可能后续状态
        
        Args:
            current_state: 当前状态
            
        Returns:
            List[WGStateEnum]: 可能的后续状态列表
        """
        return cls.STATE_TRANSITIONS.get(current_state, [])
    
    @classmethod
    def get_state_description(cls, state: WGStateEnum) -> str:
        """
        获取状态的中文描述
        
        Args:
            state: 状态枚举
            
        Returns:
            str: 状态描述
        """
        return cls.STATE_DESCRIPTIONS.get(state, state.value)
    
    @classmethod
    def is_terminal_state(cls, state: WGStateEnum) -> bool:
        """
        检查是否为终态
        
        Args:
            state: 状态枚举
            
        Returns:
            bool: 是否为终态
        """
        return len(cls.STATE_TRANSITIONS.get(state, [])) == 0
    
    @classmethod
    def is_active_state(cls, state: WGStateEnum) -> bool:
        """
        检查是否为活跃状态（非终态且非中断态）
        
        Args:
            state: 状态枚举
            
        Returns:
            bool: 是否为活跃状态
        """
        return state not in [WGStateEnum.ARCHIVED, WGStateEnum.INTERRUPTED]
    
    @classmethod
    def create_state_with_validation(cls, state_enum: WGStateEnum, 
                                   message: Optional[str] = None,
                                   serialno: Optional[str] = None) -> WGState:
        """
        创建状态对象并进行验证
        
        Args:
            state_enum: 状态枚举
            message: 状态消息
            serialno: 流水号
            
        Returns:
            WGState: 状态对象
        """
        if not isinstance(state_enum, WGStateEnum):
            raise ValueError(f"无效的状态类型: {type(state_enum)}")
        
        # 生成默认消息
        if not message:
            message = cls.get_state_description(state_enum)
        
        return WGState(
            state=state_enum,
            message=message,
            serialno=serialno,
        )
    
    @classmethod
    def transition_state(cls, current_state: WGState, new_state_enum: WGStateEnum,
                        message: Optional[str] = None) -> WGState:
        """
        执行状态转换
        
        Args:
            current_state: 当前状态对象
            new_state_enum: 新状态枚举
            message: 转换消息
            
        Returns:
            WGState: 新状态对象
            
        Raises:
            ValueError: 当状态转换非法时抛出
        """
        # 验证状态转换
        if not cls.validate_state_transition(current_state.state, new_state_enum):
            raise ValueError(
                f"非法状态转换: {current_state.state} → {new_state_enum}"
            )
        
        # 生成转换消息
        if not message:
            message = f"从{cls.get_state_description(current_state.state)}转换到{cls.get_state_description(new_state_enum)}"
        
        # 创建新状态
        new_state = WGState(
            state=new_state_enum,
            message=message,
            serialno=current_state.serialno,
        )
        
        an_logger.info(
            f"[{current_state.serialno}] 状态转换: {current_state.state} → {new_state_enum}"
        )
        
        return new_state
    
    @classmethod
    def get_state_path(cls, from_state: WGStateEnum, to_state: WGStateEnum) -> Optional[List[WGStateEnum]]:
        """
        获取从源状态到目标状态的路径（简单BFS实现）
        
        Args:
            from_state: 源状态
            to_state: 目标状态
            
        Returns:
            Optional[List[WGStateEnum]]: 状态路径，如果无法到达则返回None
        """
        if from_state == to_state:
            return [from_state]
        
        # 简单的BFS搜索
        queue = [(from_state, [from_state])]
        visited = {from_state}
        
        while queue:
            current, path = queue.pop(0)
            
            for next_state in cls.get_next_states(current):
                if next_state == to_state:
                    return path + [next_state]
                
                if next_state not in visited:
                    visited.add(next_state)
                    queue.append((next_state, path + [next_state]))
        
        return None
    
    @classmethod
    def get_all_states(cls) -> List[WGStateEnum]:
        """获取所有状态列表"""
        return list(WGStateEnum)
    
    @classmethod
    def get_state_statistics(cls) -> Dict[str, Any]:
        """获取状态统计信息"""
        all_states = cls.get_all_states()
        terminal_states = [s for s in all_states if cls.is_terminal_state(s)]
        active_states = [s for s in all_states if cls.is_active_state(s)]
        
        return {
            "total_states": len(all_states),
            "terminal_states": len(terminal_states),
            "active_states": len(active_states),
            "terminal_state_list": [s.value for s in terminal_states],
            "active_state_list": [s.value for s in active_states],
        }
