"""
无线保障智能体SAIDA架构主入口

本模块实现了WGSaidaAgent主智能体类，组装所有处理器，配置SAIDA组件，实现_run方法。
这是整个重构的最终集成，将原有的复杂状态机和链式调用模式转换为标准化的SAIDA六大处理器模式。

主要功能：
1. 继承CopilotAgent类，保持与原WGAgent的接口兼容
2. 配置SAIDA组件，包含所有六大处理器
3. 使用HeartBeatComponents进行组件注册
4. 实现_run方法，处理用户请求
5. 配置泛型类型参数，确保类型安全
6. 添加技能列表和描述信息
7. 实现智能体注册逻辑

作者: RIPER-5 重构团队
日期: 2025-07-24
版本: 1.0.0
"""

import json
from typing import ClassVar, Dict, List, Optional

from an_contract.framework.agent_entity import (
    AdditionalType,
    AgentResponse,
    AgentResponseAdditional,
)
from an_contract.framework.heartbeat_entity import HeartBeatComponents
from an_copilot.framework.agent.copilot_agent import CopilotAgent, HistoryMode
from an_copilot.framework.logging import an_logger
from an_copilot.framework.saida import Saida
from an_copilot.framework.saida.core.models.stimulus import Stimulus, StimulusType
from langchain.callbacks.manager import Callbacks
from langchain.prompts import PromptTemplate

from src.agents.wireless_guarantee_agent_saida.processors import (
    WGPerceptionProcessor,
    WGReflectionProcessor,
    WGGoalCheckerProcessor,
    WGLongTermMemoryProcessor,
    WGPlannerProcessor,
    WGResponseProcessor,
)
from src.agents.wireless_guarantee_agent_saida.wg_saida_define import (
    WG_PREDEFINED_GOALS,
    WG_PREDEFINED_KNOWLEDGE,
    WG_PREDEFINED_ONTOLOGY,
)
from src.agents.wireless_guarantee_agent_saida.wg_saida_model import (
    WGContext,
    WGGoalType,
    WGPlanExecuteResult,
    WGResponse,
    WGState,
    WGStateEnum,
)


class WGSaidaAgent(CopilotAgent):
    """
    无线保障智能体SAIDA架构主入口
    
    基于SAIDA架构重构的无线保障智能体，将原有的复杂状态机和链式调用模式
    转换为标准化的六大处理器模式，提升代码的标准化程度、可维护性和扩展性。
    """
    
    # 智能体基本信息（与原WGAgent保持一致）
    name: str = "wg_saida_agent"
    description: str = "无线保障智能体（SAIDA）"
    version: str = "1.0.0"
    history_mode = HistoryMode.ALL.value
    
    # 技能列表（与原WGAgent保持兼容，扩展SAIDA功能）
    skills: ClassVar[List[str]] = [
        "活动告警处理",
        "方案审批流程",
        "方案执行流程", 
        "效果评估流程",
        "已清除告警处理",
        "方案回退流程",
    ]
    
    # SAIDA组件配置（心跳信息只能来自于类属性）
    saida: Saida[
        WGState,
        WGGoalType,
        WGPlanExecuteResult,
        WGResponse,
        WGContext,
    ] = HeartBeatComponents(
        tag="wg_saida",
        default=Saida(
            initial_state=WGState(state=WGStateEnum.RECOGNIZING),
            perception_processor=WGPerceptionProcessor(),
            reflection_processor=WGReflectionProcessor(),
            goal_checker_processor=WGGoalCheckerProcessor(),
            long_term_memory_processor=WGLongTermMemoryProcessor(),
            planner_processor=WGPlannerProcessor(),
            response_processor=WGResponseProcessor(),
        ),
    )
    
    def __init__(self, session_id: str):
        """
        初始化无线保障智能体
        
        Args:
            session_id: 会话ID
        """
        # 延迟导入避免循环导入
        from src.config import settings
        
        super().__init__(
            session_id=session_id,
            callbacks=settings.get_tracing_factory().getTracing(),
        )
        
        # 初始化日志
        an_logger.info(f"WGSaidaAgent 初始化完成，session_id: {session_id}")
    
    def _run(
        self,
        request_id: str,
        question: str,
        tags: Optional[List[str]],
        user_agent_config: Optional[dict] = None,
        history: List[List[str]] = None,
        previous_agent_history: List[Dict[str, List[List[str]]]] = None,
        history_messages: Optional[List[Dict]] = None,
        previous_agent_history_messages: Optional[List[Dict[str, List[Dict]]]] = None,
        agent_contexts: List[str] = [],
        contexts: Optional[dict] = None,
        prompt_template: PromptTemplate = None,
        callbacks: Callbacks = None,
        reasoning: Optional[bool] = False,
    ) -> AgentResponse:
        """
        执行智能体主要逻辑
        
        Args:
            request_id: 请求ID
            question: 用户输入问题
            tags: 标签列表
            user_agent_config: 用户智能体配置
            history: 历史对话
            previous_agent_history: 前置智能体历史
            history_messages: 历史消息
            previous_agent_history_messages: 前置智能体历史消息
            agent_contexts: 智能体上下文
            contexts: 上下文字典
            prompt_template: 提示模板
            callbacks: 回调函数
            reasoning: 是否启用推理
            
        Returns:
            AgentResponse: 智能体响应
        """
        try:
            an_logger.info(f"[{request_id}] WGSaidaAgent 开始处理请求")
            
            # 创建Stimulus对象
            stimulus = self._create_stimulus(request_id, question)
            
            # 创建Context对象
            context = self._create_context(request_id, contexts)
            
            # 执行SAIDA流程
            saida_response = self.saida.stimulus(stimulus=stimulus, context=context)
            
            # 构建智能体响应
            agent_response = self._build_agent_response(
                question, saida_response, request_id
            )
            
            an_logger.info(f"[{request_id}] WGSaidaAgent 处理完成")
            return agent_response
            
        except Exception as e:
            an_logger.error(f"[{request_id}] WGSaidaAgent 执行失败: {e}")
            return self._create_error_response(question, str(e))
    
    def _create_stimulus(self, request_id: str, question: str) -> Stimulus:
        """创建Stimulus对象"""
        try:
            # 尝试解析为JSON
            if self._is_valid_json(question):
                stimulus_type = StimulusType.JSON
            else:
                stimulus_type = StimulusType.TEXT
            
            stimulus = Stimulus.create(
                session_id=self.session_id,
                request_id=request_id,
                data=question,
                type=stimulus_type,
            )
            
            an_logger.debug(f"[{request_id}] 创建Stimulus: type={stimulus_type}")
            return stimulus
            
        except Exception as e:
            an_logger.error(f"[{request_id}] 创建Stimulus失败: {e}")
            # 默认使用TEXT类型
            return Stimulus.create(
                session_id=self.session_id,
                request_id=request_id,
                data=question,
                type=StimulusType.TEXT,
            )
    
    def _create_context(self, request_id: str, contexts: Optional[dict] = None) -> WGContext:
        """创建Context对象"""
        context = WGContext(
            session_id=self.session_id,
            request_id=request_id,
        )
        
        # 添加额外的上下文信息
        if contexts:
            if "serialno" in contexts:
                context.serialno = contexts["serialno"]
            if "scheme_id" in contexts:
                context.scheme_id = contexts["scheme_id"]
            if "alarm_info" in contexts:
                context.alarm_info = contexts["alarm_info"]
        
        an_logger.debug(f"[{request_id}] 创建Context: serialno={context.serialno}")
        return context
    
    def _build_agent_response(
        self, question: str, saida_response, request_id: str
    ) -> AgentResponse:
        """构建智能体响应"""
        try:
            # 构建SAIDA附加信息
            agent_additional = [
                AgentResponseAdditional(
                    type=AdditionalType.SAIDA,
                    value=json.dumps([e.model_dump() for e in saida_response.events]),
                )
            ]
            
            # 构建智能体响应
            agent_response = AgentResponse(
                agent_name=self.name,
                agent_input=question,
                agent_output=saida_response.output,
                agent_additional=agent_additional,
            )
            
            an_logger.info(f"[{request_id}] 智能体响应构建完成")
            return agent_response
            
        except Exception as e:
            an_logger.error(f"[{request_id}] 构建智能体响应失败: {e}")
            return self._create_error_response(question, f"响应构建失败: {str(e)}")
    
    def _create_error_response(self, question: str, error_message: str) -> AgentResponse:
        """创建错误响应"""
        return AgentResponse(
            agent_name=self.name,
            agent_input=question,
            agent_output=f"处理失败：{error_message}",
            agent_additional=[],
        )
    
    def _is_valid_json(self, text: str) -> bool:
        """检查文本是否为有效的JSON格式"""
        if not text or not isinstance(text, str):
            return False
        
        text = text.strip()
        if not ((text.startswith('{') and text.endswith('}')) or 
                (text.startswith('[') and text.endswith(']'))):
            return False
        
        try:
            json.loads(text)
            return True
        except (json.JSONDecodeError, Exception):
            return False
    
    @classmethod
    def get_agent_info(cls) -> Dict[str, any]:
        """获取智能体信息"""
        return {
            "name": cls.name,
            "description": cls.description,
            "version": cls.version,
            "skills": cls.skills,
            "architecture": "SAIDA",
            "processors": [
                "WGPerceptionProcessor",
                "WGReflectionProcessor", 
                "WGGoalCheckerProcessor",
                "WGLongTermMemoryProcessor",
                "WGPlannerProcessor",
                "WGResponseProcessor",
            ],
        }


# 向 CES 注册智能体
WGSaidaAgent.register()
