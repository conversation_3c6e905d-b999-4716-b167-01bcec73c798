"""
无线保障智能体SAIDA架构模块

本模块包含基于SAIDA架构重构的无线保障智能体的完整实现，包括：
- 核心数据模型定义
- 预定义数据配置
- 六大处理器实现
- 主智能体入口

这是对原wireless_guarantee_agent的完整重构，将复杂的状态机和链式调用模式
转换为标准化的SAIDA六大处理器模式，提升代码的标准化程度、可维护性和扩展性。

作者: RIPER-5 重构团队
日期: 2025-07-24
版本: 1.0.0
"""

from .wg_saida_agent import WGSaidaAgent
from .wg_saida_model import (
    WGContext,
    WGState,
    WGStateEnum,
    WGGoalType,
    WGPlanType,
    WGPlanExecuteResult,
    WGResponse,
    AlarmPercept,
    UserInputPercept,
    AlarmInfo,
)
from .wg_saida_define import (
    WG_PREDEFINED_GOALS,
    WG_PREDEFINED_KNOWLEDGE,
    WG_PREDEFINED_ONTOLOGY,
    WG_BTREE_CODE_MAPPING,
    WG_INTENTION_BTREE_MAPPING,
)
from .processors import (
    WGPerceptionProcessor,
    WGReflectionProcessor,
    WGGoalCheckerProcessor,
    WGLongTermMemoryProcessor,
    WGPlannerProcessor,
    WGResponseProcessor,
)
from .utils import (
    WGStateManager,
    WGDatabaseOperations,
)

__all__ = [
    # 主智能体
    "WGSaidaAgent",

    # 核心数据模型
    "WGContext",
    "WGState",
    "WGStateEnum",
    "WGGoalType",
    "WGPlanType",
    "WGPlanExecuteResult",
    "WGResponse",
    "AlarmPercept",
    "UserInputPercept",
    "AlarmInfo",

    # 预定义数据
    "WG_PREDEFINED_GOALS",
    "WG_PREDEFINED_KNOWLEDGE",
    "WG_PREDEFINED_ONTOLOGY",
    "WG_BTREE_CODE_MAPPING",
    "WG_INTENTION_BTREE_MAPPING",

    # 处理器
    "WGPerceptionProcessor",
    "WGReflectionProcessor",
    "WGGoalCheckerProcessor",
    "WGLongTermMemoryProcessor",
    "WGPlannerProcessor",
    "WGResponseProcessor",

    # 工具类
    "WGStateManager",
    "WGDatabaseOperations",
]

# 版本信息
__version__ = "1.0.0"
__author__ = "RIPER-5 重构团队"
__description__ = "无线保障智能体SAIDA架构重构版本"
