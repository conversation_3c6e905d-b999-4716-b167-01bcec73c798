"""
无线保障智能体SAIDA架构核心数据模型定义

本模块定义了无线保障智能体基于SAIDA架构的所有核心数据模型，包括：
- Context: 上下文对象，保存会话状态和业务信息
- Percept: 感知对象，表示对输入的业务语义解析
- State: 状态对象，表示智能体当前的业务状态
- Goal: 目标对象，表示根据状态推导出的业务目标
- Plan: 计划对象，表示为达成目标而生成的具体执行方案
- Response: 响应对象，表示最终输出给用户的结构化结果

作者: RIPER-5 重构团队
日期: 2025-07-24
版本: 1.0.0
"""

from enum import StrEnum
from typing import Optional, Union, Dict, Any, List
from pathlib import Path

from an_copilot.framework.saida.core.models.models import (
    Context,
    Goal,
    Ontology,
    Plan,
    PlanExecuteResult,
    Response,
    SelfState,
    State,
)
from an_copilot.framework.saida.core.models.percept import Percept
from pydantic import BaseModel, Field


# ================================
# Context 上下文定义
# ================================

class WGContext(Context):
    """无线保障智能体上下文对象
    
    保存会话状态和业务信息，包括：
    - 会话标识信息
    - 告警信息
    - 状态机上下文（保持与原系统兼容）
    - 业务流程相关信息
    """
    
    # 基础会话信息
    session_id: str
    request_id: str
    
    # 业务信息
    alarm_info: Optional[Dict[str, Any]] = None
    sm_context: Optional[Dict[str, Any]] = None  # 保持原状态机上下文兼容
    intention_type: Optional[str] = None
    serialno: Optional[str] = None
    scheme_id: Optional[str] = None
    
    # 执行结果信息
    final_output: Optional[str] = None
    node_results: Optional[Dict[str, Any]] = None


# ================================
# Percept 感知对象定义
# ================================

class AlarmPercept(Percept):
    """告警感知对象
    
    表示对告警信息的业务语义解析，包含：
    - 告警基本信息
    - 意图类型
    - 输入类型（JSON/UNSTRUCTURED）
    """
    
    alarm_info: Dict[str, Any]
    intention_type: str
    input_type: str = "JSON"  # JSON 或 UNSTRUCTURED


class UserInputPercept(Percept):
    """用户输入感知对象
    
    表示对用户自然语言输入的感知解析
    """
    
    user_input: str
    intention_type: Optional[str] = None


# ================================
# State 状态定义
# ================================

class WGStateEnum(StrEnum):
    """无线保障智能体状态枚举
    
    映射原状态机的9个状态：
    - recognizing: 问题识别中
    - generating: 方案生成中  
    - approving: 方案审核中
    - executing: 方案执行中
    - evaluating: 效果评估中
    - evaluated: 已评估通过
    - resetting: 方案复位中
    - interrupted: 已中断
    - archived: 已归档
    """
    
    RECOGNIZING = "recognizing"
    GENERATING = "generating"
    APPROVING = "approving"
    EXECUTING = "executing"
    EVALUATING = "evaluating"
    EVALUATED = "evaluated"
    RESETTING = "resetting"
    INTERRUPTED = "interrupted"
    ARCHIVED = "archived"


class WGState(State):
    """无线保障智能体状态对象"""
    
    state: WGStateEnum = WGStateEnum.RECOGNIZING
    message: Optional[str] = None
    serialno: Optional[str] = None


class WGSelfState(SelfState):
    """无线保障智能体自身状态"""
    pass


# ================================
# Goal 目标定义
# ================================

class WGGoalActiveWorkflow(Goal):
    """新活动工作流目标
    
    对应意图类型1：处理新的活动告警，启动完整的保障流程
    """
    pass


class WGGoalApprovalProcess(Goal):
    """审批流程目标
    
    对应意图类型2：处理审批结果，推进方案审核流程
    """
    pass


class WGGoalExecuteProcess(Goal):
    """执行流程目标
    
    对应意图类型3：处理执行结果，推进方案执行流程
    """
    pass


class WGGoalEvaluateProcess(Goal):
    """评估流程目标
    
    对应意图类型4：处理评估结果，推进效果评估流程
    """
    pass


class WGGoalClearWorkflow(Goal):
    """清除工作流目标
    
    对应意图类型5：处理已清除告警，启动复位流程
    """
    pass


class WGGoalRollbackProcess(Goal):
    """回退流程目标
    
    对应意图类型6：处理回退结果，完成方案回退流程
    """
    pass


class WGGoalUnknown(Goal):
    """未知目标
    
    无法识别的意图类型，返回错误信息
    """
    pass


# 目标类型联合定义
WGGoalType = Union[
    WGGoalActiveWorkflow,
    WGGoalApprovalProcess,
    WGGoalExecuteProcess,
    WGGoalEvaluateProcess,
    WGGoalClearWorkflow,
    WGGoalRollbackProcess,
    WGGoalUnknown,
]


# ================================
# PlanExecuteResult 计划执行结果定义
# ================================

class WGPlanExecuteResult(PlanExecuteResult):
    """无线保障智能体计划执行结果"""
    
    session_id: str
    request_id: str
    serialno: Optional[str] = None
    scheme_id: Optional[str] = None
    intention_type: Optional[str] = None
    
    # 执行结果信息
    success: bool = True
    message: Optional[str] = None
    node_results: Optional[Dict[str, Any]] = None
    
    # 状态转换信息
    new_state: Optional[WGState] = None


# ================================
# Response 响应定义
# ================================

class WGResponse(Response):
    """无线保障智能体响应对象"""
    
    session_id: str
    request_id: str
    serialno: Optional[str] = None
    scheme_id: Optional[str] = None
    intention_type: Optional[str] = None
    
    # 输出内容
    output: str
    
    # 附加信息
    contexts: Optional[Dict[str, Any]] = Field(default_factory=dict)
    agent_additional: Optional[List[Dict[str, Any]]] = None


# ================================
# Plan 计划定义
# ================================

class WGPlanActiveWorkflow(
    Plan[WGGoalActiveWorkflow, WGContext, WGPlanExecuteResult]
):
    """新活动工作流执行计划
    
    处理新的活动告警，执行完整的保障流程：
    1. 方案生成
    2. 方案审核
    3. 方案执行
    4. 效果评估
    """
    
    btree_code: str = Field(default="")
    
    def execute(self, context: WGContext) -> WGPlanExecuteResult:
        """执行新活动工作流计划"""
        import time
        from an_copilot.framework.logging import an_logger
        from src.agents.wireless_guarantee_agent.desicion_making.goal_runner import GoalRunner
        from src.core.ces_stream_sender import CesStreamSender

        begin_time = time.time()

        try:
            # 验证行为树代码
            if not self.btree_code:
                an_logger.error("新活动工作流缺少行为树代码")
                return WGPlanExecuteResult(
                    session_id=context.session_id,
                    request_id=context.request_id,
                    serialno=context.serialno,
                    scheme_id=context.scheme_id,
                    intention_type="1",
                    success=False,
                    message="缺少行为树代码",
                )

            # 准备GoalRunner输入参数
            inputs = {
                "session_id": context.session_id,
                "request_id": context.request_id,
                "serialno": context.serialno,
                "scheme_id": context.scheme_id,
                "intention_type": "1",
            }

            # 添加告警信息
            if context.alarm_info:
                inputs.update(context.alarm_info)

            # 添加状态机上下文
            if context.sm_context:
                inputs.update(context.sm_context)

            # 创建并执行GoalRunner
            goal_runner = GoalRunner(self.btree_code)
            goal_runner.run(inputs)

            # 获取执行结果
            node_results = goal_runner.context.get("node_results") if goal_runner.context else {}

            # 更新上下文
            context.node_results = node_results
            context.final_output = f"新活动工作流执行完成，行为树代码：{self.btree_code}"

            an_logger.info(f"[{context.serialno}] 新活动工作流执行成功，行为树：{self.btree_code}")

            return WGPlanExecuteResult(
                session_id=context.session_id,
                request_id=context.request_id,
                serialno=context.serialno,
                scheme_id=context.scheme_id,
                intention_type="1",
                success=True,
                message="新活动工作流执行完成",
                node_results=node_results,
            )

        except Exception as e:
            an_logger.error(f"[{context.serialno}] 新活动工作流执行失败: {e}")
            return WGPlanExecuteResult(
                session_id=context.session_id,
                request_id=context.request_id,
                serialno=context.serialno,
                scheme_id=context.scheme_id,
                intention_type="1",
                success=False,
                message=f"新活动工作流执行失败: {str(e)}",
            )


class WGPlanApprovalProcess(
    Plan[WGGoalApprovalProcess, WGContext, WGPlanExecuteResult]
):
    """审批流程执行计划"""
    
    def execute(self, context: WGContext) -> WGPlanExecuteResult:
        """执行审批流程计划"""
        import time
        from an_copilot.framework.logging import an_logger
        from src.core.db_connector import DbConn

        begin_time = time.time()

        try:
            # 审批流程主要是数据库操作，不需要行为树
            # 处理审批结果并更新状态

            approval_result = context.alarm_info.get("approval_result") if context.alarm_info else None
            if not approval_result:
                an_logger.warning(f"[{context.serialno}] 审批流程缺少审批结果")
                return WGPlanExecuteResult(
                    session_id=context.session_id,
                    request_id=context.request_id,
                    serialno=context.serialno,
                    scheme_id=context.scheme_id,
                    intention_type="2",
                    success=False,
                    message="缺少审批结果",
                )

            # 更新上下文
            context.final_output = f"审批流程处理完成，审批结果：{approval_result}"

            # 根据审批结果确定下一状态
            if "通过" in str(approval_result):
                next_state = "executing"
                message = f"审批通过，进入执行阶段"
            else:
                next_state = "archived"
                message = f"审批不通过，流程归档"

            an_logger.info(f"[{context.serialno}] 审批流程处理完成，结果：{approval_result}")

            return WGPlanExecuteResult(
                session_id=context.session_id,
                request_id=context.request_id,
                serialno=context.serialno,
                scheme_id=context.scheme_id,
                intention_type="2",
                success=True,
                message=message,
                new_state=WGState(state=WGStateEnum.EXECUTING if "通过" in str(approval_result) else WGStateEnum.ARCHIVED),
            )

        except Exception as e:
            an_logger.error(f"[{context.serialno}] 审批流程执行失败: {e}")
            return WGPlanExecuteResult(
                session_id=context.session_id,
                request_id=context.request_id,
                serialno=context.serialno,
                scheme_id=context.scheme_id,
                intention_type="2",
                success=False,
                message=f"审批流程执行失败: {str(e)}",
            )


class WGPlanExecuteProcess(
    Plan[WGGoalExecuteProcess, WGContext, WGPlanExecuteResult]
):
    """执行流程执行计划"""

    def execute(self, context: WGContext) -> WGPlanExecuteResult:
        """执行执行流程计划"""
        import time
        from an_copilot.framework.logging import an_logger

        begin_time = time.time()

        try:
            # 执行流程主要是处理执行结果
            execute_result = context.alarm_info.get("execute_result") if context.alarm_info else None
            if not execute_result:
                an_logger.warning(f"[{context.serialno}] 执行流程缺少执行结果")
                return WGPlanExecuteResult(
                    session_id=context.session_id,
                    request_id=context.request_id,
                    serialno=context.serialno,
                    scheme_id=context.scheme_id,
                    intention_type="3",
                    success=False,
                    message="缺少执行结果",
                )

            # 更新上下文
            context.final_output = f"执行流程处理完成，执行结果：{execute_result}"

            # 根据执行结果确定下一状态
            if "成功" in str(execute_result):
                next_state = "evaluating"
                message = f"执行成功，进入评估阶段"
            else:
                next_state = "interrupted"
                message = f"执行失败，流程中断"

            an_logger.info(f"[{context.serialno}] 执行流程处理完成，结果：{execute_result}")

            return WGPlanExecuteResult(
                session_id=context.session_id,
                request_id=context.request_id,
                serialno=context.serialno,
                scheme_id=context.scheme_id,
                intention_type="3",
                success=True,
                message=message,
                new_state=WGState(state=WGStateEnum.EVALUATING if "成功" in str(execute_result) else WGStateEnum.INTERRUPTED),
            )

        except Exception as e:
            an_logger.error(f"[{context.serialno}] 执行流程执行失败: {e}")
            return WGPlanExecuteResult(
                session_id=context.session_id,
                request_id=context.request_id,
                serialno=context.serialno,
                scheme_id=context.scheme_id,
                intention_type="3",
                success=False,
                message=f"执行流程执行失败: {str(e)}",
            )


class WGPlanEvaluateProcess(
    Plan[WGGoalEvaluateProcess, WGContext, WGPlanExecuteResult]
):
    """评估流程执行计划"""

    def execute(self, context: WGContext) -> WGPlanExecuteResult:
        """执行评估流程计划"""
        import time
        from an_copilot.framework.logging import an_logger

        begin_time = time.time()

        try:
            # 评估流程主要是处理评估结果
            evaluate_result = context.alarm_info.get("evaluate_result") if context.alarm_info else None
            if not evaluate_result:
                an_logger.warning(f"[{context.serialno}] 评估流程缺少评估结果")
                return WGPlanExecuteResult(
                    session_id=context.session_id,
                    request_id=context.request_id,
                    serialno=context.serialno,
                    scheme_id=context.scheme_id,
                    intention_type="4",
                    success=False,
                    message="缺少评估结果",
                )

            # 更新上下文
            context.final_output = f"评估流程处理完成，评估结果：{evaluate_result}"

            # 根据评估结果确定下一状态
            if "成功" in str(evaluate_result) or "有效" in str(evaluate_result):
                next_state = "evaluated"
                message = f"评估成功，流程完成"
            else:
                next_state = "resetting"
                message = f"评估失败，需要复位"

            an_logger.info(f"[{context.serialno}] 评估流程处理完成，结果：{evaluate_result}")

            return WGPlanExecuteResult(
                session_id=context.session_id,
                request_id=context.request_id,
                serialno=context.serialno,
                scheme_id=context.scheme_id,
                intention_type="4",
                success=True,
                message=message,
                new_state=WGState(state=WGStateEnum.EVALUATED if "成功" in str(evaluate_result) or "有效" in str(evaluate_result) else WGStateEnum.RESETTING),
            )

        except Exception as e:
            an_logger.error(f"[{context.serialno}] 评估流程执行失败: {e}")
            return WGPlanExecuteResult(
                session_id=context.session_id,
                request_id=context.request_id,
                serialno=context.serialno,
                scheme_id=context.scheme_id,
                intention_type="4",
                success=False,
                message=f"评估流程执行失败: {str(e)}",
            )


class WGPlanClearWorkflow(
    Plan[WGGoalClearWorkflow, WGContext, WGPlanExecuteResult]
):
    """清除工作流执行计划"""

    btree_code: str = Field(default="")

    def execute(self, context: WGContext) -> WGPlanExecuteResult:
        """执行清除工作流计划"""
        import time
        from an_copilot.framework.logging import an_logger
        from src.agents.wireless_guarantee_agent.desicion_making.goal_runner import GoalRunner

        begin_time = time.time()

        try:
            # 验证行为树代码
            if not hasattr(self, 'btree_code') or not self.btree_code:
                # 清除工作流使用默认行为树代码
                btree_code = "wireless-guarantee-reset"
            else:
                btree_code = self.btree_code

            # 准备GoalRunner输入参数
            inputs = {
                "session_id": context.session_id,
                "request_id": context.request_id,
                "serialno": context.serialno,
                "scheme_id": context.scheme_id,
                "intention_type": "5",
            }

            # 添加告警信息
            if context.alarm_info:
                inputs.update(context.alarm_info)

            # 添加状态机上下文
            if context.sm_context:
                inputs.update(context.sm_context)

            # 创建并执行GoalRunner
            goal_runner = GoalRunner(btree_code)
            goal_runner.run(inputs)

            # 获取执行结果
            node_results = goal_runner.context.get("node_results") if goal_runner.context else {}

            # 更新上下文
            context.node_results = node_results
            context.final_output = f"清除工作流执行完成，行为树代码：{btree_code}"

            an_logger.info(f"[{context.serialno}] 清除工作流执行成功，行为树：{btree_code}")

            return WGPlanExecuteResult(
                session_id=context.session_id,
                request_id=context.request_id,
                serialno=context.serialno,
                scheme_id=context.scheme_id,
                intention_type="5",
                success=True,
                message="清除工作流执行完成",
                node_results=node_results,
            )

        except Exception as e:
            an_logger.error(f"[{context.serialno}] 清除工作流执行失败: {e}")
            return WGPlanExecuteResult(
                session_id=context.session_id,
                request_id=context.request_id,
                serialno=context.serialno,
                scheme_id=context.scheme_id,
                intention_type="5",
                success=False,
                message=f"清除工作流执行失败: {str(e)}",
            )


class WGPlanRollbackProcess(
    Plan[WGGoalRollbackProcess, WGContext, WGPlanExecuteResult]
):
    """回退流程执行计划"""

    def execute(self, context: WGContext) -> WGPlanExecuteResult:
        """执行回退流程计划"""
        import time
        from an_copilot.framework.logging import an_logger

        begin_time = time.time()

        try:
            # 回退流程主要是处理回退结果
            rollback_result = context.alarm_info.get("rollback_result") if context.alarm_info else None
            if not rollback_result:
                an_logger.warning(f"[{context.serialno}] 回退流程缺少回退结果")
                return WGPlanExecuteResult(
                    session_id=context.session_id,
                    request_id=context.request_id,
                    serialno=context.serialno,
                    scheme_id=context.scheme_id,
                    intention_type="6",
                    success=False,
                    message="缺少回退结果",
                )

            # 更新上下文
            context.final_output = f"回退流程处理完成，回退结果：{rollback_result}"

            # 根据回退结果确定下一状态
            if "成功" in str(rollback_result):
                next_state = "archived"
                message = f"回退成功，流程归档"
            else:
                next_state = "interrupted"
                message = f"回退失败，流程中断"

            an_logger.info(f"[{context.serialno}] 回退流程处理完成，结果：{rollback_result}")

            return WGPlanExecuteResult(
                session_id=context.session_id,
                request_id=context.request_id,
                serialno=context.serialno,
                scheme_id=context.scheme_id,
                intention_type="6",
                success=True,
                message=message,
                new_state=WGState(state=WGStateEnum.ARCHIVED if "成功" in str(rollback_result) else WGStateEnum.INTERRUPTED),
            )

        except Exception as e:
            an_logger.error(f"[{context.serialno}] 回退流程执行失败: {e}")
            return WGPlanExecuteResult(
                session_id=context.session_id,
                request_id=context.request_id,
                serialno=context.serialno,
                scheme_id=context.scheme_id,
                intention_type="6",
                success=False,
                message=f"回退流程执行失败: {str(e)}",
            )


class WGPlanUnknown(
    Plan[WGGoalUnknown, WGContext, WGPlanExecuteResult]
):
    """未知计划，无法处理"""

    def execute(self, context: WGContext) -> WGPlanExecuteResult:
        """执行未知计划，返回错误信息"""
        return WGPlanExecuteResult(
            session_id=context.session_id,
            request_id=context.request_id,
            serialno=context.serialno,
            success=False,
            message="抱歉，我无法处理这个请求",
            new_state=WGState(state=WGStateEnum.RECOGNIZING),
        )


# 计划类型联合定义
WGPlanType = Union[
    WGPlanActiveWorkflow,
    WGPlanApprovalProcess,
    WGPlanExecuteProcess,
    WGPlanEvaluateProcess,
    WGPlanClearWorkflow,
    WGPlanRollbackProcess,
    WGPlanUnknown,
]


# ================================
# Ontology 本体定义
# ================================

class WGOntology(Ontology):
    """无线保障相关的本体定义"""

    @staticmethod
    def load_schema(file_path: str) -> str:
        """加载 schema 文件内容"""
        try:
            schema_path = Path(file_path)
            if schema_path.exists():
                return schema_path.read_text(encoding="utf-8")
            else:
                raise FileNotFoundError(f"Schema file not found: {file_path}")
        except Exception as e:
            raise RuntimeError(f"Failed to load schema: {e}")


# ================================
# 业务模型定义
# ================================

class AlarmInfo(BaseModel):
    """告警信息模型

    保持与原系统的告警信息结构兼容
    """

    serialno: str
    alarm_status: str  # "活动" 或 "已清除"
    alarm_type: Optional[str] = None
    cell_name: Optional[str] = None
    start_time: Optional[str] = None
    end_time: Optional[str] = None

    # 审批相关字段
    approval_result: Optional[str] = None
    approval_user: Optional[str] = None

    # 执行相关字段
    execute_result: Optional[str] = None
    execute_time: Optional[str] = None

    # 评估相关字段
    evaluate_result: Optional[str] = None
    evaluate_time: Optional[str] = None

    # 回退相关字段
    rollback_result: Optional[str] = None
    rollback_time: Optional[str] = None
