"""
无线保障智能体SAIDA架构预定义数据配置

本模块定义了无线保障智能体的预设数据，包括：
- 预定义目标列表：6种意图对应的Goal实例
- 预定义知识列表：无线保障相关知识
- 预定义本体列表：业务本体定义
- 行为树代码映射关系
- 知识库和推送库配置

这些预定义数据供LongTermMemoryProcessor使用，确保智能体具备完整的业务知识和目标配置。

作者: RIPER-5 重构团队
日期: 2025-07-24
版本: 1.0.0
"""

from an_copilot.framework.saida.core.models.models import GoalType, Knowledge

from src.agents.wireless_guarantee_agent_saida.wg_saida_model import (
    WGGoalActiveWorkflow,
    WGGoalApprovalProcess,
    WGGoalExecuteProcess,
    WGGoalEvaluateProcess,
    WGGoalClearWorkflow,
    WGGoalRollbackProcess,
    WGGoalUnknown,
    WGOntology,
)


# ================================
# 预定义本体列表
# ================================

WG_PREDEFINED_ONTOLOGY = [
    WGOntology(
        id="wg_ontology_1",
        name="无线保障本体",
        description="无线保障相关的本体定义，包含告警、方案、状态等概念",
        owl="""
<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
         xmlns:rdfs="http://www.w3.org/2000/01/rdf-schema#"
         xmlns:owl="http://www.w3.org/2002/07/owl#"
         xmlns:wg="http://example.org/wireless-guarantee#">
  
  <owl:Ontology rdf:about="http://example.org/wireless-guarantee">
    <rdfs:label>无线保障本体</rdfs:label>
    <rdfs:comment>定义无线保障业务相关的概念和关系</rdfs:comment>
  </owl:Ontology>
  
  <!-- 告警类 -->
  <owl:Class rdf:about="http://example.org/wireless-guarantee#Alarm">
    <rdfs:label>告警</rdfs:label>
    <rdfs:comment>无线网络告警事件</rdfs:comment>
  </owl:Class>
  
  <!-- 方案类 -->
  <owl:Class rdf:about="http://example.org/wireless-guarantee#Scheme">
    <rdfs:label>保障方案</rdfs:label>
    <rdfs:comment>无线保障解决方案</rdfs:comment>
  </owl:Class>
  
  <!-- 状态类 -->
  <owl:Class rdf:about="http://example.org/wireless-guarantee#State">
    <rdfs:label>状态</rdfs:label>
    <rdfs:comment>业务流程状态</rdfs:comment>
  </owl:Class>
  
  <!-- 小区类 -->
  <owl:Class rdf:about="http://example.org/wireless-guarantee#Cell">
    <rdfs:label>小区</rdfs:label>
    <rdfs:comment>无线网络小区</rdfs:comment>
  </owl:Class>
  
</rdf:RDF>
        """,
    ),
    WGOntology(
        id="wg_ontology_2",
        name="行为树本体",
        description="行为树相关的本体定义，包含节点、条件、动作等概念",
        owl="""
<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
         xmlns:rdfs="http://www.w3.org/2000/01/rdf-schema#"
         xmlns:owl="http://www.w3.org/2002/07/owl#"
         xmlns:bt="http://example.org/behavior-tree#">
  
  <owl:Ontology rdf:about="http://example.org/behavior-tree">
    <rdfs:label>行为树本体</rdfs:label>
    <rdfs:comment>定义行为树决策相关的概念和关系</rdfs:comment>
  </owl:Ontology>
  
  <!-- 行为树节点类 -->
  <owl:Class rdf:about="http://example.org/behavior-tree#Node">
    <rdfs:label>行为树节点</rdfs:label>
    <rdfs:comment>行为树的基本执行单元</rdfs:comment>
  </owl:Class>
  
  <!-- 条件节点类 -->
  <owl:Class rdf:about="http://example.org/behavior-tree#Condition">
    <rdfs:label>条件节点</rdfs:label>
    <rdfs:comment>行为树的条件判断节点</rdfs:comment>
    <rdfs:subClassOf rdf:resource="http://example.org/behavior-tree#Node"/>
  </owl:Class>
  
  <!-- 动作节点类 -->
  <owl:Class rdf:about="http://example.org/behavior-tree#Action">
    <rdfs:label>动作节点</rdfs:label>
    <rdfs:comment>行为树的动作执行节点</rdfs:comment>
    <rdfs:subClassOf rdf:resource="http://example.org/behavior-tree#Node"/>
  </owl:Class>
  
</rdf:RDF>
        """,
    ),
]


# ================================
# 预定义知识列表
# ================================

WG_PREDEFINED_KNOWLEDGE = [
    Knowledge(
        id="wg_knowledge_1",
        name="无线保障基础知识"
    ),
    Knowledge(
        id="wg_knowledge_2",
        name="告警处理知识"
    ),
    Knowledge(
        id="wg_knowledge_3",
        name="方案生成知识"
    ),
    Knowledge(
        id="wg_knowledge_4",
        name="效果评估知识"
    ),
    Knowledge(
        id="wg_knowledge_5",
        name="行为树决策知识"
    ),
    Knowledge(
        id="wg_knowledge_6",
        name="状态机管理知识"
    ),
]


# ================================
# 预定义目标列表
# ================================

WG_PREDEFINED_GOALS = [
    # 意图类型1：新活动工作流
    WGGoalActiveWorkflow(
        id="wg_goal_active_workflow",
        name="新活动工作流",
        description="处理新的活动告警，启动完整的保障流程，包括方案生成、审核、执行和评估",
        type=GoalType.BTREE,
    ),
    
    # 意图类型2：审批流程
    WGGoalApprovalProcess(
        id="wg_goal_approval_process", 
        name="审批流程",
        description="处理方案审批结果，推进审核流程，根据审批结果决定后续操作",
        type=GoalType.NORMAL,
    ),
    
    # 意图类型3：执行流程
    WGGoalExecuteProcess(
        id="wg_goal_execute_process",
        name="执行流程", 
        description="处理方案执行结果，推进执行流程，监控执行状态和效果",
        type=GoalType.NORMAL,
    ),
    
    # 意图类型4：评估流程
    WGGoalEvaluateProcess(
        id="wg_goal_evaluate_process",
        name="评估流程",
        description="处理效果评估结果，推进评估流程，分析保障效果和改进建议",
        type=GoalType.NORMAL,
    ),
    
    # 意图类型5：清除工作流
    WGGoalClearWorkflow(
        id="wg_goal_clear_workflow",
        name="清除工作流",
        description="处理已清除告警，启动复位流程，清理相关资源和状态",
        type=GoalType.BTREE,
    ),
    
    # 意图类型6：回退流程
    WGGoalRollbackProcess(
        id="wg_goal_rollback_process",
        name="回退流程",
        description="处理方案回退结果，完成回退流程，恢复原始状态和配置",
        type=GoalType.NORMAL,
    ),
    
    # 未知意图
    WGGoalUnknown(
        id="wg_goal_unknown",
        name="未知目标",
        description="无法识别的意图类型，返回错误信息并重置状态",
        type=GoalType.NORMAL,
    ),
]


# ================================
# 行为树代码映射配置
# ================================

# 行为树代码映射关系（与config.json中的btree_codes对应）
WG_BTREE_CODE_MAPPING = {
    # 方案生成行为树
    "start_generate_scheme_process": "wireless-guarantee-scheme-generate",
    # 复位流程行为树  
    "start_reset_process": "wireless-guarantee-reset",
}

# 意图类型到行为树代码的映射
WG_INTENTION_BTREE_MAPPING = {
    "1": "wireless-guarantee-scheme-generate",  # 新活动工作流
    "5": "wireless-guarantee-reset",            # 清除工作流
}


# ================================
# 知识库配置
# ================================

# 知识库仓库配置
WG_KNOWLEDGE_REPOSITORIES = [
    "无线保障知识库",
    "无线保障消息推送库",
]

# 推送消息仓库配置
WG_PUSH_REPOSITORIES = [
    "无线保障消息推送库",
]

# 知识库查询配置
WG_KNOWLEDGE_QUERIES = {
    # 状态感知阶段查询
    "状态感知": ["意图1状态感知", "意图5状态感知"],
    # 意图识别阶段查询
    "意图识别": ["意图1意图识别", "意图5意图识别"], 
    # 目标管理阶段查询
    "目标管理": ["意图1目标管理", "意图5目标管理"],
    # 任务规划阶段查询
    "任务规划": ["意图1任务规划", "意图5任务规划"],
    # 任务执行阶段查询
    "任务执行": ["任务执行", "软参优化补偿方案总结"],
    # 方案详情阶段查询
    "方案详情": ["方案详情"],
}


# ================================
# 数据库表配置
# ================================

# 数据库表名配置（与db_config.txt对应）
WG_DATABASE_TABLES = {
    "alarm_table": "wireless_guarantee.tm_alarm_task_info",
    "state_history_table": "wireless_guarantee.tm_task_state_history", 
    "evaluate_history_table": "wireless_guarantee.tm_task_evaluate_history",
    "report_table": "wireless_guarantee.tm_alarm_report",
}


# ================================
# 消息阶段配置
# ================================

# 完整消息发送的意图类型
WG_COMPLETE_MESSAGE_INTENTS = {"1", "5"}

# 活动消息状态列表
WG_ACTIVE_MESSAGE_STATES = ["状态感知", "意图识别", "目标管理", "任务规划"]

# 所有消息状态列表
WG_ALL_MESSAGE_STATES = ["状态感知", "意图识别", "目标管理", "任务规划", "任务执行", "方案详情"]
