"""
无线保障智能体SAIDA架构处理器模块

本模块包含无线保障智能体的六大处理器实现：
- WGPerceptionProcessor: 感知处理器
- WGReflectionProcessor: 反思处理器  
- WGGoalCheckerProcessor: 目标检查处理器
- WGLongTermMemoryProcessor: 长期记忆处理器
- WGPlannerProcessor: 规划处理器
- WGResponseProcessor: 响应处理器

作者: RIPER-5 重构团队
日期: 2025-07-24
版本: 1.0.0
"""

from .wg_perception_processor import WGPerceptionProcessor
from .wg_reflection_processor import WGReflectionProcessor
from .wg_goal_checker_processor import WGGoalCheckerProcessor
from .wg_long_term_memory_processor import WGLongTermMemoryProcessor
from .wg_planner_processor import WGPlannerProcessor
from .wg_response_processor import WGResponseProcessor

__all__ = [
    "WGPerceptionProcessor",
    "WGReflectionProcessor",
    "WGGoalCheckerProcessor",
    "WGLongTermMemoryProcessor",
    "WGPlannerProcessor",
    "WGResponseProcessor",
]
