"""
无线保障智能体反思处理器

本模块实现了WGReflectionProcessor，负责根据感知结果和历史信息推导当前业务状态。
集成了原InfoExtractionChain的信息提取逻辑，实现状态推理和上下文更新。

主要功能：
1. 根据感知结果和历史信息推导业务状态
2. 集成原InfoExtractionChain的数据库操作逻辑
3. 实现状态转换验证和上下文更新
4. 保持与原状态机的状态推理兼容性

作者: RIPER-5 重构团队
日期: 2025-07-24
版本: 1.0.0
"""

import json
import time
from typing import Dict, Any, Optional, List, ClassVar

from an_copilot.framework.logging import an_logger
from an_copilot.framework.saida.core.interfaces import IReflectionProcessor
from an_copilot.framework.saida.core.models.percept import Percept

from src.agents.wireless_guarantee_agent_saida.wg_saida_model import (
    WGContext,
    WGState,
    WGStateEnum,
    AlarmPercept,
    UserInputPercept,
    AlarmInfo,
)
from src.core.db_connector import DbConn, ALARM_TABLE, EVALUATE_HISTORY_TABLE


class WGReflectionProcessor(IReflectionProcessor[Percept, WGState, WGContext]):
    """
    无线保障智能体反思处理器
    
    根据感知结果和历史信息推导当前业务状态，集成原InfoExtractionChain的核心逻辑。
    实现状态推理、数据库操作和上下文更新，保持与原状态机的兼容性。
    """
    
    # 意图类型常量（与原系统保持一致）
    INTENT_ACTIVE_WORKFLOW: ClassVar[str] = "1"
    INTENT_APPROVAL_PROCESS: ClassVar[str] = "2"
    INTENT_EXECUTE_PROCESS: ClassVar[str] = "3"
    INTENT_EVALUATE_PROCESS: ClassVar[str] = "4"
    INTENT_CLEAR_WORKFLOW: ClassVar[str] = "5"
    INTENT_ROLLBACK_PROCESS: ClassVar[str] = "6"
    
    # 数据库操作配置（与原InfoExtractionChain完全一致）
    DB_OPERATIONS: ClassVar[Dict[str, Dict[str, Any]]] = {
        INTENT_APPROVAL_PROCESS: {
            'type': 'insert',
            'table': ALARM_TABLE,
            'keys': ['approval_result', 'approval_user', 'approval_time'],
            'extra_fields': {'serialno': 'serialno'}
        },
        INTENT_EXECUTE_PROCESS: {
            'type': 'insert',
            'table': ALARM_TABLE,
            'keys': ['execute_result', 'execute_time'],
            'extra_fields': {'serialno': 'serialno'}
        },
        INTENT_EVALUATE_PROCESS: {
            'type': 'insert',
            'table': EVALUATE_HISTORY_TABLE,
            'keys': [
                'serialno', 'scheme_id', 'evaluate_time', 'evaluate_round', 'evaluate_result',
                'evaluate_indicators', 'evaluate_period_type', 'evaluate_start_time', 'evaluate_end_time'
            ]
        },
        INTENT_CLEAR_WORKFLOW: {
            'type': 'update',
            'table': ALARM_TABLE,
            'keys': ['clear_time'],
            'condition': lambda info: info.get("clear_time")
        }
    }
    
    # 意图类型到状态的映射关系
    INTENT_STATE_MAPPING: ClassVar[Dict[str, WGStateEnum]] = {
        INTENT_ACTIVE_WORKFLOW: WGStateEnum.RECOGNIZING,
        INTENT_APPROVAL_PROCESS: WGStateEnum.APPROVING,
        INTENT_EXECUTE_PROCESS: WGStateEnum.EXECUTING,
        INTENT_EVALUATE_PROCESS: WGStateEnum.EVALUATING,
        INTENT_CLEAR_WORKFLOW: WGStateEnum.RESETTING,
        INTENT_ROLLBACK_PROCESS: WGStateEnum.RESETTING,
    }
    
    # 意图类型与状态兼容性验证
    INTENTION_STATE_COMPATIBILITY: ClassVar[Dict[str, List[str]]] = {
        INTENT_ACTIVE_WORKFLOW: ["recognizing"],
        INTENT_APPROVAL_PROCESS: ["approving"],
        INTENT_EXECUTE_PROCESS: ["executing"],
        INTENT_EVALUATE_PROCESS: ["evaluating"],
        INTENT_ROLLBACK_PROCESS: ["resetting"],
        INTENT_CLEAR_WORKFLOW: ["recognizing", "generating", "approving", "executing", 
                               "evaluating", "evaluated", "resetting", "interrupted"],  # 可从任意状态触发
    }
    
    def __init__(self):
        """初始化反思处理器"""
        # 延迟初始化避免序列化问题
        self._settings = None

    @property
    def settings(self):
        """延迟初始化settings"""
        if self._settings is None:
            from src.config import settings
            self._settings = settings
        return self._settings
    
    def process(
        self,
        percept: Percept,
        percept_history: Optional[List[Percept]],
        state: WGState,
        context: WGContext,
    ) -> WGState:
        """
        根据感知结果和历史信息推导当前业务状态
        
        Args:
            percept: 当前感知对象
            percept_history: 历史感知对象列表
            state: 当前状态
            context: 上下文对象
            
        Returns:
            WGState: 推导出的新状态
            
        Raises:
            ValueError: 当感知类型不支持或状态推理失败时抛出
        """
        try:
            if isinstance(percept, AlarmPercept):
                return self._process_alarm_percept(percept, state, context)
            elif isinstance(percept, UserInputPercept):
                return self._process_user_input_percept(percept, state, context)
            else:
                raise ValueError(f"不支持的感知类型: {type(percept)}")
                
        except Exception as e:
            an_logger.error(f"反思处理失败: {e}")
            return WGState(
                state=WGStateEnum.INTERRUPTED,
                message=f"状态推理失败: {str(e)}",
                serialno=context.serialno,
            )
    
    def _process_alarm_percept(self, percept: AlarmPercept, state: WGState, context: WGContext) -> WGState:
        """处理告警感知对象"""
        intention_type = percept.intention_type
        alarm_info = percept.alarm_info
        serialno = alarm_info.get("serialno")
        
        # 验证意图类型与当前状态的兼容性
        current_state_str = state.state.value if state.state else "recognizing"
        if not self._validate_intention_state_compatibility(intention_type, current_state_str):
            return WGState(
                state=state.state,  # 保持当前状态
                message=f"意图类型 {intention_type} 不兼容当前状态 {current_state_str}",
                serialno=serialno,
            )
        
        # 执行数据库操作（与原InfoExtractionChain逻辑一致）
        self._execute_database_operation(intention_type, alarm_info, serialno)
        
        # 更新上下文信息
        self._update_context(context, alarm_info, intention_type, serialno)
        
        # 推导新状态
        new_state_enum = self._infer_new_state(intention_type, alarm_info, current_state_str)
        
        # 生成状态消息
        state_message = self._generate_state_message(intention_type, alarm_info, new_state_enum)
        
        an_logger.info(f"状态推理完成: {current_state_str} -> {new_state_enum.value}, 流水号: {serialno}")
        
        return WGState(
            state=new_state_enum,
            message=state_message,
            serialno=serialno,
        )
    
    def _process_user_input_percept(self, percept: UserInputPercept, state: WGState, context: WGContext) -> WGState:
        """处理用户输入感知对象"""
        if not percept.user_input:
            return WGState(
                state=WGStateEnum.INTERRUPTED,
                message="用户输入不能为空",
                serialno=context.serialno,
            )
        
        # 对于用户输入，通常保持当前状态或转为识别状态
        return WGState(
            state=WGStateEnum.RECOGNIZING,
            message="接收到用户输入，进入识别状态",
            serialno=context.serialno,
        )
    
    def _validate_intention_state_compatibility(self, intention_type: str, current_state: str) -> bool:
        """验证意图类型与当前状态的兼容性（与原状态机逻辑一致）"""
        allowed_states = self.INTENTION_STATE_COMPATIBILITY.get(intention_type, [])
        if not allowed_states:
            an_logger.warning(f"未知的意图类型: {intention_type}")
            return False
        
        is_compatible = current_state in allowed_states
        if not is_compatible:
            an_logger.warning(
                f"意图类型 {intention_type} 不兼容当前状态 {current_state}，"
                f"允许的状态: {allowed_states}"
            )
        
        return is_compatible
    
    def _execute_database_operation(self, intention_type: str, alarm_info: Dict[str, Any], serialno: str):
        """执行数据库操作（与原InfoExtractionChain完全一致）"""
        operation_config = self.DB_OPERATIONS.get(intention_type)
        if not operation_config:
            return
        
        # 检查条件（如果有）
        if 'condition' in operation_config and not operation_config['condition'](alarm_info):
            return
        
        db = None
        try:
            db = DbConn()
            
            if operation_config['type'] == 'insert':
                self._execute_insert(db, operation_config, alarm_info, serialno)
            elif operation_config['type'] == 'update':
                self._execute_update(db, operation_config, alarm_info, serialno)
                
        except Exception as e:
            an_logger.error(f"信息提取入库失败：{e}")
        finally:
            if db:
                db.close()
    
    def _execute_insert(self, db: DbConn, config: Dict, alarm_info: Dict[str, Any], serialno: str):
        """执行INSERT操作（与原InfoExtractionChain完全一致）"""
        insert_info = self._subdict(alarm_info, config['keys'])
        
        # 处理额外字段
        if 'extra_fields' in config:
            for field, source in config['extra_fields'].items():
                if source == 'serialno':
                    insert_info[field] = serialno
        
        if insert_info:  # 只有在有数据时才执行
            columns = ', '.join(insert_info.keys())
            placeholders = ', '.join(['%s'] * len(insert_info))
            values = tuple(insert_info.values())
            sql = f"INSERT INTO {config['table']} ({columns}) VALUES ({placeholders})"
            db.execute_sql_withdf(values, sql)
            an_logger.info(f"数据库INSERT操作成功: {config['table']}, 数据: {insert_info}")
    
    def _execute_update(self, db: DbConn, config: Dict, alarm_info: Dict[str, Any], serialno: str):
        """执行UPDATE操作（与原InfoExtractionChain完全一致）"""
        update_info = self._subdict(alarm_info, config['keys'])
        
        if update_info:
            set_clause = ', '.join([f"{key} = %s" for key in update_info.keys()])
            values = list(update_info.values()) + [serialno]
            sql = f"UPDATE {config['table']} SET {set_clause} WHERE serialno = %s"
            db.execute_sql_withdf(values, sql)
            an_logger.info(f"数据库UPDATE操作成功: {config['table']}, 数据: {update_info}")
    
    def _subdict(self, data: Dict[str, Any], keys: List[str]) -> Dict[str, Any]:
        """提取字典的子集（与原InfoExtractionChain完全一致）"""
        return {key: data[key] for key in keys if key in data and data[key] is not None}
    
    def _update_context(self, context: WGContext, alarm_info: Dict[str, Any], 
                       intention_type: str, serialno: str):
        """更新上下文信息"""
        # 更新基础信息
        context.alarm_info = alarm_info
        context.intention_type = intention_type
        context.serialno = serialno
        
        # 更新状态机上下文（保持与原系统兼容）
        if not context.sm_context:
            context.sm_context = {}
        
        context.sm_context.update(alarm_info)
        context.sm_context["intention_type"] = intention_type
        context.sm_context["session_id"] = context.session_id
        context.sm_context["request_id"] = context.request_id
        
        # 添加输入类型信息
        if "input_type" in alarm_info:
            context.sm_context["input_type"] = alarm_info["input_type"]
    
    def _infer_new_state(self, intention_type: str, alarm_info: Dict[str, Any], 
                        current_state: str) -> WGStateEnum:
        """推导新状态（基于原状态机逻辑）"""
        # 基本的意图到状态映射
        base_state = self.INTENT_STATE_MAPPING.get(intention_type, WGStateEnum.RECOGNIZING)
        
        # 根据具体的业务逻辑进行状态推理
        if intention_type == self.INTENT_ACTIVE_WORKFLOW:
            # 新活动工作流：从识别状态开始
            return WGStateEnum.RECOGNIZING
        
        elif intention_type == self.INTENT_APPROVAL_PROCESS:
            # 审批流程：根据审批结果决定后续状态
            approval_result = alarm_info.get("approval_result")
            if approval_result and "通过" in str(approval_result):
                return WGStateEnum.EXECUTING  # 审批通过，进入执行状态
            else:
                return WGStateEnum.ARCHIVED   # 审批不通过，归档
        
        elif intention_type == self.INTENT_EXECUTE_PROCESS:
            # 执行流程：根据执行结果决定后续状态
            execute_result = alarm_info.get("execute_result")
            if execute_result and "成功" in str(execute_result):
                return WGStateEnum.EVALUATING  # 执行成功，进入评估状态
            else:
                return WGStateEnum.INTERRUPTED  # 执行失败，中断
        
        elif intention_type == self.INTENT_EVALUATE_PROCESS:
            # 评估流程：根据评估结果决定后续状态
            evaluate_result = alarm_info.get("evaluate_result")
            if evaluate_result and "成功" in str(evaluate_result):
                return WGStateEnum.RESETTING   # 评估成功，进入复位状态
            else:
                return WGStateEnum.INTERRUPTED  # 评估失败，中断
        
        elif intention_type == self.INTENT_CLEAR_WORKFLOW:
            # 清除工作流：进入复位状态
            return WGStateEnum.RESETTING
        
        elif intention_type == self.INTENT_ROLLBACK_PROCESS:
            # 回退流程：根据回退结果决定最终状态
            rollback_result = alarm_info.get("rollback_result")
            if rollback_result and "成功" in str(rollback_result):
                return WGStateEnum.ARCHIVED    # 回退成功，归档
            else:
                return WGStateEnum.INTERRUPTED  # 回退失败，中断
        
        # 默认返回基础状态
        return base_state
    
    def _generate_state_message(self, intention_type: str, alarm_info: Dict[str, Any], 
                               new_state: WGStateEnum) -> str:
        """生成状态消息"""
        intention_descriptions = {
            self.INTENT_ACTIVE_WORKFLOW: "新活动工作流",
            self.INTENT_APPROVAL_PROCESS: "审批流程",
            self.INTENT_EXECUTE_PROCESS: "执行流程",
            self.INTENT_EVALUATE_PROCESS: "评估流程",
            self.INTENT_CLEAR_WORKFLOW: "清除工作流",
            self.INTENT_ROLLBACK_PROCESS: "回退流程",
        }
        
        state_descriptions = {
            WGStateEnum.RECOGNIZING: "问题识别中",
            WGStateEnum.GENERATING: "方案生成中",
            WGStateEnum.APPROVING: "方案审核中",
            WGStateEnum.EXECUTING: "方案执行中",
            WGStateEnum.EVALUATING: "效果评估中",
            WGStateEnum.EVALUATED: "已评估通过",
            WGStateEnum.RESETTING: "方案复位中",
            WGStateEnum.INTERRUPTED: "已中断",
            WGStateEnum.ARCHIVED: "已归档",
        }
        
        intent_desc = intention_descriptions.get(intention_type, f"意图{intention_type}")
        state_desc = state_descriptions.get(new_state, new_state.value)
        
        return f"处理{intent_desc}，当前状态：{state_desc}"
