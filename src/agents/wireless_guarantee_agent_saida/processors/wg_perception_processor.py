"""
无线保障智能体感知处理器

本模块实现了WGPerceptionProcessor，负责将输入的Stimulus转换为业务感知对象Percept。
集成了原IntentRecognitionChain的意图识别逻辑，支持JSON和自然语言输入的解析和意图分类。

主要功能：
1. 解析输入的告警信息（支持JSON格式和自然语言）
2. 根据告警状态和类型进行意图分类
3. 创建对应的感知对象
4. 发送感知阶段消息

作者: RIPER-5 重构团队
日期: 2025-07-24
版本: 1.0.0
"""

import json
import time
from typing import Dict, Any, Optional, List, ClassVar

from an_copilot.framework.logging import an_logger
from an_copilot.framework.saida.core.interfaces import IPerceptionProcessor
from an_copilot.framework.saida.core.models.percept import Percept
from an_copilot.framework.saida.core.models.stimulus import Stimulus, StimulusType
from langchain.prompts import PromptTemplate
from pydantic import ValidationError

from src.agents.wireless_guarantee_agent_saida.wg_saida_model import (
    WGContext,
    AlarmPercept,
    UserInputPercept,
    AlarmInfo,
)
from src.core.ces_stream_sender import CesStreamSender
from src.utils.knowledge_util import similarity_search


class WGPerceptionProcessor(IPerceptionProcessor[Stimulus, Percept, WGContext]):
    """
    无线保障智能体感知处理器
    
    将输入的Stimulus转换为业务感知对象Percept，集成原IntentRecognitionChain的核心逻辑。
    支持多种输入格式的解析和意图分类，确保与原系统的意图识别准确率一致。
    """
    
    # 意图类型常量（与原IntentRecognitionChain保持一致）
    INTENT_ACTIVE_WORKFLOW: ClassVar[str] = "1"
    INTENT_APPROVAL_PROCESS: ClassVar[str] = "2"
    INTENT_EXECUTE_PROCESS: ClassVar[str] = "3"
    INTENT_EVALUATE_PROCESS: ClassVar[str] = "4"
    INTENT_CLEAR_WORKFLOW: ClassVar[str] = "5"
    INTENT_ROLLBACK_PROCESS: ClassVar[str] = "6"
    
    # 意图分类规则映射（与原系统完全一致）
    INTENT_RULES: ClassVar[List[tuple]] = [
        (lambda info: info.get("alarm_status") == "活动", INTENT_ACTIVE_WORKFLOW, "新活动工作流"),
        (lambda info: "approval_result" in info, INTENT_APPROVAL_PROCESS, "审批流程"),
        (lambda info: "execute_result" in info, INTENT_EXECUTE_PROCESS, "执行流程"),
        (lambda info: "evaluate_result" in info, INTENT_EVALUATE_PROCESS, "评估流程"),
        (lambda info: info.get("alarm_status") == "已清除", INTENT_CLEAR_WORKFLOW, "清除工作流"),
        (lambda info: "rollback_result" in info, INTENT_ROLLBACK_PROCESS, "回退流程"),
    ]
    
    # 完整消息发送的意图类型
    COMPLETE_INTENTS: ClassVar[set] = {"1", "5"}
    
    # 消息阶段配置
    ACTIVE_MESSAGE_STATES: ClassVar[List[str]] = ["状态感知", "意图识别", "目标管理", "任务规划"]
    
    # 知识库配置
    PUSH_REPOSITORY: ClassVar[List[str]] = ["无线保障消息推送库"]
    
    def __init__(self):
        """初始化感知处理器"""
        # 延迟初始化避免序列化问题
        self._ces_stream_sender = None
        self._settings = None

    @property
    def ces_stream_sender(self):
        """延迟初始化CesStreamSender"""
        if self._ces_stream_sender is None:
            from src.config import settings
            self._ces_stream_sender = CesStreamSender(settings.ces_stream)
        return self._ces_stream_sender

    @property
    def settings(self):
        """延迟初始化settings"""
        if self._settings is None:
            from src.config import settings
            self._settings = settings
        return self._settings
    
    def process(self, stimulus: Stimulus, context: WGContext) -> Percept:
        """
        处理输入的Stimulus，转换为业务感知对象Percept
        
        Args:
            stimulus: 输入刺激对象
            context: 上下文对象
            
        Returns:
            Percept: 感知对象
            
        Raises:
            ValueError: 当输入无法解析或意图无法识别时抛出
        """
        begin_time = time.time()
        
        try:
            # 根据Stimulus类型进行处理
            if stimulus.type is StimulusType.TEXT:
                return self._process_text_stimulus(stimulus, context, begin_time)
            elif stimulus.type is StimulusType.JSON:
                return self._process_json_stimulus(stimulus, context, begin_time)
            else:
                raise ValueError(f"不支持的Stimulus类型: {stimulus.type}")
                
        except Exception as e:
            an_logger.error(f"感知处理失败: {e}")
            # 发送错误消息
            self.ces_stream_sender.send_msg(
                context.session_id,
                context.request_id,
                f"感知处理失败: {str(e)}",
                begin_time
            )
            raise
    
    def _process_text_stimulus(self, stimulus: Stimulus, context: WGContext, begin_time: float) -> Percept:
        """处理TEXT类型的Stimulus"""
        message = stimulus.data
        
        # 尝试解析为告警信息
        try:
            # 第一步：尝试直接JSON解析
            if self._is_valid_json(message):
                alarm_info = self._try_parse_json(message)
                if alarm_info:
                    an_logger.debug("直接JSON解析成功")
                    return self._create_alarm_percept(alarm_info, "JSON", context, begin_time)
            
            # 第二步：使用LLM辅助格式化
            an_logger.warning("直接解析失败，使用LLM辅助格式化")
            alarm_info = self._try_llm_format(message, context)
            if alarm_info:
                return self._create_alarm_percept(alarm_info, "UNSTRUCTURED", context, begin_time)
            
            # 第三步：作为用户输入处理
            an_logger.info("作为用户输入处理")
            return UserInputPercept(
                user_input=message,
                source_stimulus=stimulus,
            )
            
        except ValidationError as e:
            an_logger.debug(f"告警信息验证失败: {e}")
            # 作为用户输入处理
            return UserInputPercept(
                user_input=message,
                source_stimulus=stimulus,
            )
    
    def _process_json_stimulus(self, stimulus: Stimulus, context: WGContext, begin_time: float) -> Percept:
        """处理JSON类型的Stimulus"""
        try:
            if isinstance(stimulus.data, dict):
                alarm_info = stimulus.data
            else:
                alarm_info = json.loads(stimulus.data)
            
            return self._create_alarm_percept(alarm_info, "JSON", context, begin_time)
            
        except (json.JSONDecodeError, Exception) as e:
            an_logger.error(f"JSON Stimulus解析失败: {e}")
            raise ValueError(f"无法解析JSON Stimulus: {e}")
    
    def _create_alarm_percept(self, alarm_info: Dict[str, Any], input_type: str, 
                             context: WGContext, begin_time: float) -> AlarmPercept:
        """创建告警感知对象"""
        # 进行意图分类
        intention_type = self._intent_classification(alarm_info)
        if intention_type is None:
            an_logger.error(f"无法识别的意图类型，告警信息：{alarm_info}")
            raise ValueError(f"无法识别的意图类型")
        
        # 更新上下文
        context.alarm_info = alarm_info
        context.intention_type = intention_type
        context.serialno = alarm_info.get("serialno")
        
        # 发送感知阶段消息（仅对完整意图类型）
        if intention_type in self.COMPLETE_INTENTS:
            self._send_perception_messages(intention_type, alarm_info, context, begin_time)
        
        # 创建告警感知对象
        return AlarmPercept(
            alarm_info=alarm_info,
            intention_type=intention_type,
            input_type=input_type,
            source_stimulus=None,  # 可以根据需要设置
        )
    
    def _intent_classification(self, alarm_info: Dict[str, Any]) -> Optional[str]:
        """基于告警信息进行意图分类（与原系统逻辑完全一致）"""
        # 遍历意图规则进行匹配
        for rule_func, intent_type, description in self.INTENT_RULES:
            if rule_func(alarm_info):
                self._log_intent_recognition(alarm_info, intent_type, description)
                return intent_type
        
        # 无法识别意图
        an_logger.warning(f"无法识别意图类型，告警信息：{alarm_info}")
        return None
    
    def _log_intent_recognition(self, alarm_info: Dict[str, Any], intent_type: str, description: str):
        """记录意图识别结果"""
        serialno = alarm_info.get('serialno')
        if intent_type == self.INTENT_ACTIVE_WORKFLOW:
            an_logger.info(f"识别为{description}，告警流水号：{serialno}")
        else:
            result_key = self._get_result_key_for_intent(intent_type)
            result_value = alarm_info.get(result_key, '未知') if result_key else ''
            if result_value:
                an_logger.info(f"[{serialno}] 识别为{description}，{result_key.replace('_', '')}：{result_value}")
            else:
                an_logger.info(f"[{serialno}] 识别为{description}")
    
    def _get_result_key_for_intent(self, intent_type: str) -> Optional[str]:
        """根据意图类型获取结果字段名"""
        result_mapping = {
            self.INTENT_APPROVAL_PROCESS: "approval_result",
            self.INTENT_EXECUTE_PROCESS: "execute_result",
            self.INTENT_EVALUATE_PROCESS: "evaluate_result",
            self.INTENT_ROLLBACK_PROCESS: "rollback_result",
        }
        return result_mapping.get(intent_type)
    
    def _send_perception_messages(self, intention_type: str, alarm_info: Dict[str, Any], 
                                 context: WGContext, begin_time: float):
        """发送感知阶段消息"""
        try:
            for stage in self.ACTIVE_MESSAGE_STATES:
                query = f"意图{intention_type}{stage}"
                content = self._generate_content_from_knowledge(query, alarm_info)
                self.ces_stream_sender.send_msg(
                    context.session_id,
                    context.request_id,
                    f"\n{content}",
                    begin_time
                )
        except Exception as e:
            an_logger.error(f"发送感知消息失败: {e}")
    
    def _generate_content_from_knowledge(self, query: str, alarm_info: Dict[str, Any]) -> str:
        """从知识库生成内容"""
        try:
            reference_documents_array = self._search_knowledge_base(query, self.PUSH_REPOSITORY)
            if reference_documents_array:
                reference_documents = reference_documents_array[0]
                
                # 使用模板生成内容
                prompt = PromptTemplate.from_template(reference_documents)
                # 这里简化处理，实际可以根据需要调用LLM
                return prompt.format(input=json.dumps(alarm_info, ensure_ascii=False))
            else:
                return f"感知阶段：{query}"
        except Exception as e:
            an_logger.error(f"从知识库生成内容失败: {e}")
            return f"感知阶段：{query}"
    
    def _search_knowledge_base(self, query: str, repository: List[str]) -> List[str]:
        """搜索知识库"""
        try:
            an_logger.info(f"[WGPerceptionProcessor] 调用similarity_search: 查询='{query}', repository_alias={repository}")
            reference_documents_array = similarity_search(query, repository_alias=repository)
            an_logger.info(f"[WGPerceptionProcessor] similarity_search返回结果数量: {len(reference_documents_array)}")
            return reference_documents_array
        except Exception as e:
            an_logger.error(f"知识库搜索失败: {e}")
            return []
    
    def _is_valid_json(self, text: str) -> bool:
        """快速检查文本是否可能是有效的JSON格式"""
        if not text or not isinstance(text, str):
            return False
        
        text = text.strip()
        return (text.startswith('{') and text.endswith('}')) or \
               (text.startswith('[') and text.endswith(']'))
    
    def _try_parse_json(self, message: str) -> Optional[Dict[str, Any]]:
        """尝试直接解析JSON"""
        try:
            alarm_info = json.loads(message, strict=False)
            if isinstance(alarm_info, dict):
                return alarm_info
        except (json.JSONDecodeError, Exception) as e:
            an_logger.debug(f"JSON解析失败: {e}")
        return None
    
    def _try_llm_format(self, message: str, context: WGContext) -> Optional[Dict[str, Any]]:
        """尝试使用LLM格式化输入"""
        try:
            # 这里简化实现，实际应该调用LLM进行格式化
            # 由于缺少LLM相关的依赖，暂时返回None
            an_logger.warning("LLM格式化功能暂未实现，需要在后续任务中完善")
            return None
        except Exception as e:
            an_logger.error(f"LLM辅助格式化失败：{e}")
            self.ces_stream_sender.send_msg(
                context.session_id,
                context.request_id,
                f"请检查输入内容格式"
            )
        return None
