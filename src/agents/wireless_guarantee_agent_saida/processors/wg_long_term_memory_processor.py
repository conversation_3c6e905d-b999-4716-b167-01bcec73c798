"""
无线保障智能体长期记忆处理器

本模块实现了WGLongTermMemoryProcessor，负责管理预定义的目标、知识、本体等静态数据。
提供统一的数据访问接口，支持数据加载、缓存、查询和验证功能。

主要功能：
1. 管理预定义的目标、知识、本体数据
2. 提供统一的数据访问接口
3. 实现数据缓存和刷新机制
4. 支持数据查询和过滤功能
5. 提供数据有效性验证

作者: RIPER-5 重构团队
日期: 2025-07-24
版本: 1.0.0
"""

import time
from typing import List, Optional, Dict, Any, ClassVar

from an_copilot.framework.logging import an_logger
from an_copilot.framework.saida.core.interfaces import ILongTermMemoryProcessor
from an_copilot.framework.saida.core.models.models import Knowledge

from src.agents.wireless_guarantee_agent_saida.wg_saida_model import (
    WGGoalType,
    WGOntology,
)
from src.agents.wireless_guarantee_agent_saida.wg_saida_define import (
    WG_PREDEFINED_GOALS,
    WG_PREDEFINED_KNOWLEDGE,
    WG_PREDEFINED_ONTOLOGY,
    WG_BTREE_CODE_MAPPING,
    WG_INTENTION_BTREE_MAPPING,
    WG_KNOWLEDGE_REPOSITORIES,
    WG_PUSH_REPOSITORIES,
    WG_KNOWLEDGE_QUERIES,
    WG_DATABASE_TABLES,
    WG_COMPLETE_MESSAGE_INTENTS,
    WG_ACTIVE_MESSAGE_STATES,
    WG_ALL_MESSAGE_STATES,
)


class WGLongTermMemoryProcessor(ILongTermMemoryProcessor[WGGoalType, WGOntology]):
    """
    无线保障智能体长期记忆处理器
    
    负责管理预定义的目标、知识、本体等静态数据，提供统一的数据访问接口。
    支持数据缓存、查询、过滤和验证功能，确保数据的完整性和访问效率。
    """
    
    # 缓存过期时间（秒）
    CACHE_EXPIRY_TIME: ClassVar[int] = 3600  # 1小时
    
    def __init__(self):
        """初始化长期记忆处理器"""
        # 数据缓存
        self._goals_cache: Optional[List[WGGoalType]] = None
        self._knowledge_cache: Optional[List[Knowledge]] = None
        self._ontology_cache: Optional[List[WGOntology]] = None
        
        # 缓存时间戳
        self._goals_cache_time: float = 0
        self._knowledge_cache_time: float = 0
        self._ontology_cache_time: float = 0
        
        # 数据验证标志
        self._data_validated: bool = False
        
        an_logger.info("WGLongTermMemoryProcessor 初始化完成")
    
    def load_goals(self) -> List[WGGoalType]:
        """
        加载预定义的目标列表
        
        Returns:
            List[WGGoalType]: 预定义目标列表
        """
        current_time = time.time()
        
        # 检查缓存是否有效
        if (self._goals_cache is not None and 
            current_time - self._goals_cache_time < self.CACHE_EXPIRY_TIME):
            an_logger.debug("从缓存加载目标列表")
            return self._goals_cache
        
        try:
            # 从预定义数据加载
            goals = list(WG_PREDEFINED_GOALS)
            
            # 验证数据有效性
            self._validate_goals(goals)
            
            # 更新缓存
            self._goals_cache = goals
            self._goals_cache_time = current_time
            
            an_logger.info(f"成功加载 {len(goals)} 个预定义目标")
            return goals
            
        except Exception as e:
            an_logger.error(f"加载预定义目标失败: {e}")
            # 返回空列表而不是抛出异常，确保系统稳定性
            return []
    
    def load_knowledge(self) -> List[Knowledge]:
        """
        加载预定义的知识列表
        
        Returns:
            List[Knowledge]: 预定义知识列表
        """
        current_time = time.time()
        
        # 检查缓存是否有效
        if (self._knowledge_cache is not None and 
            current_time - self._knowledge_cache_time < self.CACHE_EXPIRY_TIME):
            an_logger.debug("从缓存加载知识列表")
            return self._knowledge_cache
        
        try:
            # 从预定义数据加载
            knowledge = list(WG_PREDEFINED_KNOWLEDGE)
            
            # 验证数据有效性
            self._validate_knowledge(knowledge)
            
            # 更新缓存
            self._knowledge_cache = knowledge
            self._knowledge_cache_time = current_time
            
            an_logger.info(f"成功加载 {len(knowledge)} 个预定义知识")
            return knowledge
            
        except Exception as e:
            an_logger.error(f"加载预定义知识失败: {e}")
            # 返回空列表而不是抛出异常，确保系统稳定性
            return []
    
    def load_ontology(self) -> List[WGOntology]:
        """
        加载预定义的本体列表
        
        Returns:
            List[WGOntology]: 预定义本体列表
        """
        current_time = time.time()
        
        # 检查缓存是否有效
        if (self._ontology_cache is not None and 
            current_time - self._ontology_cache_time < self.CACHE_EXPIRY_TIME):
            an_logger.debug("从缓存加载本体列表")
            return self._ontology_cache
        
        try:
            # 从预定义数据加载
            ontologies = list(WG_PREDEFINED_ONTOLOGY)
            
            # 验证数据有效性
            self._validate_ontologies(ontologies)
            
            # 更新缓存
            self._ontology_cache = ontologies
            self._ontology_cache_time = current_time
            
            an_logger.info(f"成功加载 {len(ontologies)} 个预定义本体")
            return ontologies
            
        except Exception as e:
            an_logger.error(f"加载预定义本体失败: {e}")
            # 返回空列表而不是抛出异常，确保系统稳定性
            return []
    
    def get_btree_code_mapping(self) -> Dict[str, str]:
        """获取行为树代码映射关系"""
        return dict(WG_BTREE_CODE_MAPPING)
    
    def get_intention_btree_mapping(self) -> Dict[str, str]:
        """获取意图类型到行为树代码的映射"""
        return dict(WG_INTENTION_BTREE_MAPPING)
    
    def get_knowledge_repositories(self) -> List[str]:
        """获取知识库仓库配置"""
        return list(WG_KNOWLEDGE_REPOSITORIES)
    
    def get_push_repositories(self) -> List[str]:
        """获取推送消息仓库配置"""
        return list(WG_PUSH_REPOSITORIES)
    
    def get_knowledge_queries(self) -> Dict[str, List[str]]:
        """获取知识库查询配置"""
        return dict(WG_KNOWLEDGE_QUERIES)
    
    def get_database_tables(self) -> Dict[str, str]:
        """获取数据库表配置"""
        return dict(WG_DATABASE_TABLES)
    
    def get_complete_message_intents(self) -> set:
        """获取完整消息发送的意图类型"""
        return set(WG_COMPLETE_MESSAGE_INTENTS)
    
    def get_active_message_states(self) -> List[str]:
        """获取活动消息状态列表"""
        return list(WG_ACTIVE_MESSAGE_STATES)
    
    def get_all_message_states(self) -> List[str]:
        """获取所有消息状态列表"""
        return list(WG_ALL_MESSAGE_STATES)
    
    def query_goals_by_type(self, goal_type: str) -> List[WGGoalType]:
        """根据类型查询目标"""
        goals = self.load_goals()
        return [goal for goal in goals if goal.__class__.__name__.endswith(goal_type)]
    
    def query_knowledge_by_name(self, name: str) -> List[Knowledge]:
        """根据名称查询知识"""
        knowledge = self.load_knowledge()
        return [k for k in knowledge if name.lower() in k.name.lower()]
    
    def query_ontology_by_name(self, name: str) -> List[WGOntology]:
        """根据名称查询本体"""
        ontologies = self.load_ontology()
        return [o for o in ontologies if name.lower() in o.name.lower()]
    
    def refresh_cache(self):
        """刷新所有缓存"""
        an_logger.info("刷新长期记忆缓存")
        self._goals_cache = None
        self._knowledge_cache = None
        self._ontology_cache = None
        self._goals_cache_time = 0
        self._knowledge_cache_time = 0
        self._ontology_cache_time = 0
        self._data_validated = False
    
    def validate_all_data(self) -> bool:
        """验证所有预定义数据的有效性"""
        try:
            goals = self.load_goals()
            knowledge = self.load_knowledge()
            ontologies = self.load_ontology()
            
            self._validate_goals(goals)
            self._validate_knowledge(knowledge)
            self._validate_ontologies(ontologies)
            
            self._data_validated = True
            an_logger.info("所有预定义数据验证通过")
            return True
            
        except Exception as e:
            an_logger.error(f"数据验证失败: {e}")
            self._data_validated = False
            return False
    
    def is_data_valid(self) -> bool:
        """检查数据是否有效"""
        return self._data_validated
    
    def get_cache_status(self) -> Dict[str, Any]:
        """获取缓存状态信息"""
        current_time = time.time()
        return {
            "goals_cached": self._goals_cache is not None,
            "knowledge_cached": self._knowledge_cache is not None,
            "ontology_cached": self._ontology_cache is not None,
            "goals_cache_age": current_time - self._goals_cache_time if self._goals_cache_time > 0 else -1,
            "knowledge_cache_age": current_time - self._knowledge_cache_time if self._knowledge_cache_time > 0 else -1,
            "ontology_cache_age": current_time - self._ontology_cache_time if self._ontology_cache_time > 0 else -1,
            "cache_expiry_time": self.CACHE_EXPIRY_TIME,
            "data_validated": self._data_validated,
        }
    
    def _validate_goals(self, goals: List[WGGoalType]):
        """验证目标数据有效性"""
        if not goals:
            raise ValueError("目标列表不能为空")
        
        for goal in goals:
            if not hasattr(goal, 'id') or not goal.id:
                raise ValueError(f"目标缺少有效的ID: {goal}")
            if not hasattr(goal, 'name') or not goal.name:
                raise ValueError(f"目标缺少有效的名称: {goal}")
            if not hasattr(goal, 'description') or not goal.description:
                raise ValueError(f"目标缺少有效的描述: {goal}")
        
        an_logger.debug(f"目标数据验证通过，共 {len(goals)} 个目标")
    
    def _validate_knowledge(self, knowledge: List[Knowledge]):
        """验证知识数据有效性"""
        if not knowledge:
            raise ValueError("知识列表不能为空")

        for k in knowledge:
            if not hasattr(k, 'id') or not k.id:
                raise ValueError(f"知识缺少有效的ID: {k}")
            if not hasattr(k, 'name') or not k.name:
                raise ValueError(f"知识缺少有效的名称: {k}")

        an_logger.debug(f"知识数据验证通过，共 {len(knowledge)} 个知识")
    
    def _validate_ontologies(self, ontologies: List[WGOntology]):
        """验证本体数据有效性"""
        if not ontologies:
            raise ValueError("本体列表不能为空")
        
        for ontology in ontologies:
            if not hasattr(ontology, 'id') or not ontology.id:
                raise ValueError(f"本体缺少有效的ID: {ontology}")
            if not hasattr(ontology, 'name') or not ontology.name:
                raise ValueError(f"本体缺少有效的名称: {ontology}")
            if not hasattr(ontology, 'owl') or not ontology.owl:
                raise ValueError(f"本体缺少有效的OWL定义: {ontology}")
        
        an_logger.debug(f"本体数据验证通过，共 {len(ontologies)} 个本体")
