import json
from typing import Any, Dict, Optional, List

import requests
from an_copilot.framework.chain.copilot_chain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LLmGeneration
from an_copilot.framework.logging import an_logger
from langchain.prompts import PromptTemplate

from src.utils.oauth_util import get_oauth_token
from src.core.db_connector import DbConn

from datetime import datetime

ACTIVE_PHASE_INTENTS = {"1", "2", "3", "4"}
CLEARED_PHASE_INTENTS = {"5", "6"}


def similarity_search(text: str, repository_alias: List[str], top_k: int = 1) -> List[str]:
    # 延迟导入settings，避免循环导入
    from src.config import settings

    access_token = get_oauth_token()  # 如果通过CES访问，那么不需要此步骤
    url = f"{settings.config.wg_agent_config.an_copilot_knowledge_base_url}/v1/api/knowledge/repository/vectors/similarity_search"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}",  # 如果通过CES访问，那么不需要此步骤
    }
    data = {
        "text": text,
        "top_k": top_k,
        "embeder_name": "HuggingFace",
        "repository_alias": repository_alias,
    }

    response = requests.post(url, headers=headers, data=json.dumps(data))

    if response.status_code == 200:
        segment_contents = []
        rerank_segments = response.json()["data"]["segments"]
        for segment in rerank_segments:
            segment_contents.append(segment["segment_content"])
        return segment_contents
    else:
        response.raise_for_status()  # 抛出异常以处理错误响应

# 生成报告和界面输出内容(字符串)的函数 (需要调用similarity_search) 返回类型为字符串 根据query生成内容
def generate_content(chain: CopilotChain, query: str, repository: list, inputs: Dict[str, Any], run_manager) -> str:
    """从知识库生成内容"""
    an_logger.info(f"调用similarity_search: 查询='{query}', repository_alias={repository}")
    reference_documents_array = similarity_search(query, repository_alias=repository)  # 获取相似的文档列表
    an_logger.info(f"similarity_search返回结果数量: {len(reference_documents_array)}")

    if not reference_documents_array:
        an_logger.error(f"[RagAlarmReportChain] similarity_search返回空数组，查询='{query}'")
        raise ValueError(f"知识库查询'{query}'返回空结果")

    reference_documents = reference_documents_array[0]  # 取所有相似文档中第一个
    prompt = PromptTemplate.from_template(reference_documents)
    prompt_value = prompt.format_prompt(**inputs)  # 根据输入的数据(字典形式)生成提示词
    response = chain.llm_generate(
        messages=[prompt_value.to_messages()], run_manager=run_manager
    )  # 生成回复
    return response.content

def save_report_to_db(sm_context: Dict[str, Any], intention_type: str, report_content: str):
    """保存报告到数据库"""
    serialno = sm_context.get("serialno")
    phase = _determine_phase(intention_type)

    if not serialno or not phase:
        return
    db_con = None
    try:
        db_con = DbConn()
        insert_sql = f"INSERT INTO {db_con.report_table} (serialno, phase, report_content, report_time) VALUES (%s, %s, %s, %s)"
        db_con.execute_sql_withdf((serialno, phase, report_content, datetime.now()), insert_sql)
    except Exception as e:
        an_logger.error(f"保存报告到数据库失败: {e}")
        raise
    finally:
        if db_con:
            db_con.close()

def _determine_phase(intention_type: str) -> Optional[str]:
    """根据意图类型确定阶段"""
    if intention_type in ACTIVE_PHASE_INTENTS:
        return "ACTIVE"
    elif intention_type in CLEARED_PHASE_INTENTS:
        return "CLEARED"
    return None



if __name__ == '__main__':
    reference_documents_array = similarity_search('muaa', repository_alias='SD业务体验保障')
    print(reference_documents_array)


