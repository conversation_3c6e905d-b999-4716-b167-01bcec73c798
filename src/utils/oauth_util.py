import requests


def get_oauth_token():
    """只是演示如何通过程序获取 OAuth2.0 访问令牌"""
    # 延迟导入settings，避免循环导入
    from src.config import settings

    url = f"{settings.config.wg_agent_config.an_oauth_base_url}/oauth/token"
    headers = {
        "Authorization": "Basic bmM6bmM=",
        "Content-Type": "application/x-www-form-urlencoded",
    }
    data = {
        "username": "copilot_ces",
        "password": "Copilot!ces@2023",
        "grant_type": "password",
        "scope": "server",
    }

    response = requests.post(url, headers=headers, data=data)

    if response.status_code == 200:
        response_json = response.json()
        return response_json.get("access_token")
    else:
        response.raise_for_status()
