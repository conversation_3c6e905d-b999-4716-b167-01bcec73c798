#!/bin/bash

# Change to the correct directory to ensure proper module resolution
cd /usr/app/gpt

# Check startup mode (default to 'run' for backward compatibility)
STARTUP_MODE=${STARTUP_MODE:-run}

if [ "${STARTUP_MODE}" = "chat" ]; then
  echo "Starting in chat mode with streamlit..."
  if [ -z "${OTEL_EXPORTER_OTLP_ENDPOINT}" ]; then
    streamlit run src/chat.py --server.address=0.0.0.0 --server.port=5000
  else
    printenv | grep "^OTEL_"
    opentelemetry-instrument streamlit run src/chat.py --server.address=0.0.0.0 --server.port=5000
  fi
else
  echo "Starting in run mode with API server..."
  if [ -z "${OTEL_EXPORTER_OTLP_ENDPOINT}" ]; then
    python src/main.py -H 0.0.0.0 -P 5000
  else
    printenv | grep "^OTEL_"
    opentelemetry-instrument python src/main.py -H 0.0.0.0 -P 5000
  fi
fi

