#!/bin/bash

# ================================
# WG-Copilot-GPT Docker构建脚本
# 适用于CentOS 7构建服务器
# ================================

set -e

# 配置参数
BUILD_DATE=$(date +"%Y%m%d%H%M%S")
PROJECT_ROOT="/opt/wg-copilot-gpt"  # 项目根目录（上传文件的目标位置）

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Docker环境
check_docker() {
    log_step "检查Docker环境..."
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        log_error "Docker服务未启动或权限不足"
        exit 1
    fi

    log_info "Docker环境检查通过"
}

# 构建Docker镜像
build_docker_func(){
    log_step "开始构建Docker镜像: $IMAGE_NAME"

    # 记录构建开始时间
    BUILD_START=$(date +%s)

    # 构建镜像 - 使用离线模式
    docker build \
        --network=host \
        --no-cache \
        --progress=plain \
        --build-arg BUILD_DATE="$BUILD_DATE" \
        --build-arg VERSION="$PROJECT_VERSION" \
        --build-arg PIP_INDEX_URL="http://nexus.oss.asiainfo.com:8099/nexus/repository/pypi-group/simple/" \
        --build-arg PIP_TRUSTED_HOST="nexus.oss.asiainfo.com" \
        -t "$IMAGE_NAME" \
        -f Dockerfile .

    # 计算构建时间
    BUILD_END=$(date +%s)
    BUILD_TIME=$((BUILD_END - BUILD_START))

    log_info "Docker镜像构建完成: $IMAGE_NAME"
    log_info "构建耗时: ${BUILD_TIME}秒"

    # 显示镜像信息
    docker images | grep "$PROJECT_NAME" | head -5
}

# 导出Docker镜像
export_docker_func(){
    log_step "导出Docker镜像..."

    EXPORT_FILE="${PROJECT_NAME}_${PLATFORM}_${PROJECT_VERSION}.tar"

    docker save -o "$EXPORT_FILE" "$IMAGE_NAME"

    if [ -f "$EXPORT_FILE" ]; then
        FILE_SIZE=$(du -h "$EXPORT_FILE" | cut -f1)
        log_info "镜像导出成功: $EXPORT_FILE (大小: $FILE_SIZE)"
        log_info "请将此文件上传到PaaS平台"
    else
        log_error "镜像导出失败"
        exit 1
    fi
}

# 推送到Harbor仓库（可选）
push_docker_func(){
    if [ -n "${HARBOR_ADDRESS}" ]; then
        log_step "推送镜像到Harbor仓库..."

        # 登录Harbor
        echo "$HARBOR_PASSWORD" | docker login "$HARBOR_ADDRESS" -u "$HARBOR_USERNAME" --password-stdin

        # 标记镜像
        HARBOR_IMAGE="$PROJECT_NAME:$PROJECT_VERSION"
        docker tag "$IMAGE_NAME" "$HARBOR_IMAGE"

        # 推送镜像
        docker push "$HARBOR_IMAGE"

        log_info "镜像推送成功: $HARBOR_IMAGE"

        # 清理本地Harbor标记
        docker rmi "$HARBOR_IMAGE" || true
    else
        log_warn "未配置Harbor地址，跳过推送步骤"
    fi
}

# 检查项目文件
check_project_files() {
    log_step "检查项目文件..."

    if [ ! -d "$PROJECT_ROOT" ]; then
        log_error "项目目录不存在: $PROJECT_ROOT"
        log_error "请确保已将项目文件上传到构建服务器"
        exit 1
    fi

    cd "$PROJECT_ROOT"

    # 检查必要文件
    local required_files=("pyproject.toml" "src" "docker/Dockerfile" "docker/startup.sh")
    for file in "${required_files[@]}"; do
        if [ ! -e "$file" ]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done

    # 检查内网PyPI源连通性
    log_step "检查内网PyPI源连通性..."
    if ! curl -s --connect-timeout 10 "http://nexus.oss.asiainfo.com:8099/nexus/repository/pypi-group/simple/" > /dev/null; then
        log_error "无法连接到内网PyPI源: http://nexus.oss.asiainfo.com:8099/nexus/repository/pypi-group/simple/"
        log_error "请检查网络配置或联系管理员"
        exit 1
    fi
    log_info "内网PyPI源连通性检查通过"

    log_info "项目文件检查通过"
}

# 清理函数
cleanup() {
    log_step "清理临时文件..."
    cd "$PROJECT_ROOT/docker"

    # 清理复制的文件
    rm -f pyproject.toml uv.lock startup.sh
    rm -rf src

    log_info "清理完成"
}

# 主函数
main(){
    log_info "=== WG-Copilot-GPT Docker构建脚本 (本地文件版) ==="

    # 检查Docker环境
    check_docker

    # 检查项目文件
    check_project_files

    # 处理版本参数
    if [ -n "$1" ]; then
        log_info "使用指定版本标签：$1"
        VERSION_OVERRIDE="$1"
    else
        log_info "使用项目配置文件中的版本"
        VERSION_OVERRIDE=""
    fi

    # 进入项目根目录
    cd "$PROJECT_ROOT"

    # 设置清理陷阱
    trap cleanup EXIT

    # 复制必要文件到docker目录
    log_step "准备构建文件..."
    cp pyproject.toml docker/
    cp -r src docker/
    cp docker/startup.sh docker/startup.sh.tmp && mv docker/startup.sh.tmp docker/startup.sh

    # 提取项目信息
    PROJECT_NAME=$(grep 'name = ' pyproject.toml | head -1 | awk -F'"' '{print $2}')
    if [ -n "$VERSION_OVERRIDE" ]; then
        PROJECT_VERSION="$VERSION_OVERRIDE"
    else
        PROJECT_VERSION=$(grep 'version = ' pyproject.toml | head -1 | awk -F'"' '{print $2}')
    fi
    PLATFORM=$(uname -m)
    IMAGE_NAME="${PROJECT_NAME}_${PLATFORM}:${PROJECT_VERSION}"

    cd docker

    # 显示构建信息
    echo "===================================================================="
    log_info "构建信息汇总:"
    echo "  当前工作目录: $(pwd)"
    echo "  项目根目录: $PROJECT_ROOT"
    echo "  项目名称: $PROJECT_NAME"
    echo "  项目版本: $PROJECT_VERSION"
    echo "  目标平台: $PLATFORM"
    echo "  镜像名称: $IMAGE_NAME"
    echo "  构建时间: $(date)"
    echo "===================================================================="

    # 确认构建
    while true; do
        read -p "$(echo -e ${YELLOW}将制作镜像 $IMAGE_NAME, 确认请输入 [Y/N]:${NC} )" input

        case $input in
            [yY]|[yY][eE][sS])
                build_docker_func
                export_docker_func
                push_docker_func

                log_info "=== 构建完成 ==="
                log_info "镜像名称: $IMAGE_NAME"
                log_info "导出文件: ${PROJECT_NAME}_${PLATFORM}_${PROJECT_VERSION}.tar"
                log_info "请将导出的tar文件上传到PaaS平台进行部署"
                exit 0
                ;;
            [nN]|[nN][oO])
                log_info "用户取消构建"
                exit 0
                ;;
            *)
                log_error "无效输入，请输入 Y 或 N"
                ;;
        esac
    done
}

# 脚本入口
main "$@"
