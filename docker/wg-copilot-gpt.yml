version: '3.2'
services:
  wg-copilot-gpt:
    image: retina/wg-copilot-gpt_x86_64:1.2.0
    hostname: wg-copilot-gpt
    container_name: wg-copilot-gpt
    security_opt:
      - seccomp=unconfined
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
    environment:
      - LLM_ID=qwen2-72b
      - CES_REMOTE_CONFIG_ENABLED=true
      - GUARDRAIL_INPUT_ENABLED=false
      - GUARDRAIL_OUTPUT_ENABLED=false
      - LANGCHAIN_DEBUG=true
      - EXPERIMENTAL=true
      - SWAGGER_UI_ENABLED=true
      - CES_ENABLED=true
      - CES_HEARTBEAT_INTERVAL=30
      - CES_URI=http://************:6066 # an-copilot-ces 访问地址
      - CES_HEARTBEAT_SOURCE_HOST=*********** # 本服务外部可访问 IP，默认 127.0.0.1
      - CES_HEARTBEAT_SOURCE_PORT=5005
      - PROJECT_VERSION=1.0.0
    ports:
      - "5005:5000"
