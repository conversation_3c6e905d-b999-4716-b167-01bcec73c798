# ================================
# 多阶段构建 - 构建阶段
# ================================
FROM coolbeevip/langchain-cpu:3.11-0.3.21 as builder

# 设置时区
RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 设置工作目录
WORKDIR /usr/app/gpt

# 创建虚拟环境
RUN python -m venv /usr/app/venv
ENV PATH="/usr/app/venv/bin:$PATH"

# 配置pip使用内网源（环境变量方式，确保所有pip操作都使用内网源）
ENV PIP_INDEX_URL="http://nexus.oss.asiainfo.com:8099/nexus/repository/pypi-group/simple/" \
    PIP_TRUSTED_HOST="nexus.oss.asiainfo.com" \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_NO_CACHE_DIR=1

# 复制项目文件
COPY pyproject.toml .
COPY src src
COPY startup.sh startup.sh

# 安装依赖 - 完全使用内网源，禁用外网访问
RUN pip install . '.[lint]' '.[test]' '.[package]' \
    --index-url http://nexus.oss.asiainfo.com:8099/nexus/repository/pypi-group/simple/ \
    --trusted-host nexus.oss.asiainfo.com \
    --default-timeout=600 \
    --no-warn-script-location \
    --disable-pip-version-check

# 清理不必要的包以减少镜像大小
RUN rm -rf /usr/app/venv/lib/python3.11/site-packages/torch

# ================================
# 多阶段构建 - 生产阶段
# ================================
FROM python:3.11-slim as production

# 设置时区（使用已有的时区文件，不需要网络）
RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 创建非root用户（避免任何网络操作）
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 配置pip使用内网源（确保生产阶段也使用内网源）
ENV PIP_INDEX_URL="http://nexus.oss.asiainfo.com:8099/nexus/repository/pypi-group/simple/" \
    PIP_TRUSTED_HOST="nexus.oss.asiainfo.com" \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_NO_CACHE_DIR=1

# 设置工作目录
WORKDIR /usr/app/gpt

# 创建日志目录并设置权限
RUN mkdir -p /usr/app/gpt/logs && \
    chown -R appuser:appuser /usr/app/gpt

# 从构建阶段复制文件
COPY --from=builder --chown=appuser:appuser /usr/app/venv ../venv
COPY --from=builder --chown=appuser:appuser /usr/app/gpt/src src
COPY --from=builder --chown=appuser:appuser /usr/app/gpt/startup.sh startup.sh

# 设置执行权限
RUN chmod +x startup.sh

# 设置环境变量
ENV OTEL_SERVICE_NAME=wg-copilot-gpt \
    OTEL_TRACES_EXPORTER=otlp \
    OTEL_METRICS_EXPORTER=otlp \
    CYTHON_COMPATIBLE=true \
    PYTHONPATH=/usr/app/gpt/src:$PYTHONPATH \
    PATH="/usr/app/venv/bin:$PATH" \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

# 暴露端口
EXPOSE 5000

# 数据卷
VOLUME ["/usr/app/gpt/logs"]

# 启动命令
CMD ["sh", "startup.sh"]
