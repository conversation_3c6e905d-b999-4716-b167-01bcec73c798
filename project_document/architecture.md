# 无线保障智能体SAIDA重构架构设计

## 架构设计概述

### 设计目标
将 `wireless_guarantee_agent` 重构为基于SAIDA框架的标准化智能体，在保持业务功能完整性的前提下，提升代码的标准化程度、可维护性和扩展性。

### 核心设计原则
1. **业务逻辑保持**: 确保所有原有业务功能正常工作
2. **架构标准化**: 采用SAIDA框架的标准处理器模式
3. **渐进式迁移**: 分阶段实施，降低风险
4. **性能优化**: 在标准化的同时保持或提升性能

## 组件映射关系

### 核心组件映射表

| 原WG组件 | 新SAIDA组件 | 职责映射 |
|----------|-------------|----------|
| IntentRecognitionChain | WGPerceptionProcessor | 意图识别 → 感知转换 |
| InfoExtractionChain | WGReflectionProcessor | 信息提取 → 状态推理 |
| 意图到目标映射逻辑 | WGGoalCheckerProcessor | 意图分类 → 目标选择 |
| 预定义数据 | WGLongTermMemoryProcessor | 静态数据管理 |
| 计划生成逻辑 | WGPlannerProcessor | 目标 → 计划生成 |
| GoalRunner + 状态机 | WGPlan.execute() | 行为树执行 + 状态转换 |
| 报告生成链 | WGResponseProcessor | 结果 → 响应封装 |

### 数据流转设计

```mermaid
graph LR
    A[Stimulus] --> B[WGPerceptionProcessor]
    B --> C[WGPercept]
    C --> D[WGReflectionProcessor]
    D --> E[WGState]
    E --> F[WGGoalCheckerProcessor]
    F --> G[WGGoal]
    G --> H[WGPlannerProcessor]
    H --> I[WGPlan]
    I --> J[Plan.execute()]
    J --> K[WGPlanExecuteResult]
    K --> L[WGResponseProcessor]
    L --> M[WGResponse]
```

## 核心数据模型设计

### 状态枚举设计
```python
class WGStateEnum(StrEnum):
    RECOGNIZING = "recognizing"      # 问题识别中
    GENERATING = "generating"        # 方案生成中
    APPROVING = "approving"          # 方案审核中
    EXECUTING = "executing"          # 方案执行中
    EVALUATING = "evaluating"        # 效果评估中
    EVALUATED = "evaluated"          # 已评估通过
    RESETTING = "resetting"          # 方案复位中
    INTERRUPTED = "interrupted"      # 已中断
    ARCHIVED = "archived"            # 已归档
```

### 目标类型设计
```python
class WGGoalActiveWorkflow(Goal):      # 意图类型1: 新活动工作流
class WGGoalApprovalProcess(Goal):     # 意图类型2: 审批流程
class WGGoalExecuteProcess(Goal):      # 意图类型3: 执行流程
class WGGoalEvaluateProcess(Goal):     # 意图类型4: 评估流程
class WGGoalClearWorkflow(Goal):       # 意图类型5: 清除工作流
class WGGoalRollbackProcess(Goal):     # 意图类型6: 回退流程
```

### 上下文设计
```python
class WGContext(Context):
    session_id: str
    request_id: str
    alarm_info: Optional[dict] = None
    sm_context: Optional[dict] = None
    intention_type: Optional[str] = None
    serialno: Optional[str] = None
    scheme_id: Optional[str] = None
```

## 目录结构设计

```
wireless_guarantee_agent_saida/
├── wg_saida_agent.py           # 主智能体入口
├── wg_saida_model.py           # 核心数据模型
├── wg_saida_define.py          # 预定义数据
├── processors/                 # 六大处理器
│   ├── __init__.py
│   ├── wg_perception_processor.py
│   ├── wg_reflection_processor.py
│   ├── wg_goal_checker_processor.py
│   ├── wg_long_term_memory_processor.py
│   ├── wg_planner_processor.py
│   └── wg_response_processor.py
├── chains/                     # 辅助处理链
│   ├── __init__.py
│   └── wg_validation_chain.py
├── decision_making/            # 决策组件
│   ├── __init__.py
│   └── goal_runner.py
├── utils/                      # 工具类
│   ├── __init__.py
│   ├── state_manager.py
│   └── db_operations.py
└── docs/
    └── architecture.md
```

## 关键技术实现策略

### 1. 状态持久化策略
- 通过WGContext保存状态机上下文信息
- 在Plan执行过程中更新数据库状态
- 利用SAIDA的事件机制记录状态变更

### 2. 行为树集成策略
- 在Plan.execute()中创建GoalRunner实例
- 保持原有的行为树执行逻辑
- 将行为树结果反馈到状态更新中

### 3. 并发管理策略
- 移除GlobalRegistry，依赖SAIDA框架的实例管理
- 通过session_id区分不同的业务实例
- 在Context中维护必要的缓存信息

### 4. 数据库交互策略
- 主要在Plan.execute()中进行数据库操作
- 通过Context传递数据库连接信息
- 保持原有的数据库表结构和操作逻辑

## 风险评估与缓解策略

### 主要风险
1. **业务逻辑丢失风险 (高)**: 详细映射文档，分阶段验证
2. **性能下降风险 (中)**: 性能基准测试，必要时优化
3. **数据一致性风险 (高)**: 保持原有数据库结构，渐进式迁移
4. **集成复杂性风险 (中)**: 保持GoalRunner独立性，适配器模式集成

### 成功标准
1. 功能完整性：所有原有功能正常工作
2. 性能指标：响应时间不超过原系统的120%
3. 代码质量：代码复杂度降低，可维护性提升
4. 兼容性：与现有系统的接口保持兼容

## 预期收益

### 技术收益
- 统一的SAIDA框架提高代码一致性
- 清晰的处理器职责分离
- 标准化的错误处理和日志
- 框架级别的监控和调试支持

### 业务收益
- 降低新开发者的学习成本
- 便于与其他SAIDA智能体集成
- 支持插件化的业务逻辑扩展
- 减少技术债务和维护成本
