import re, json, pytest
from src.agents.wireless_guarantee_agent.wg_agent import WGAgent
from src.agents.wireless_guarantee_agent.reflection.state_machine import WGStateMachine
from src.agents.wireless_guarantee_agent.reflection.state_router import StateMachineFactory
from an_copilot.framework import bootstrap
from an_copilot.framework.agent.copilot_agent import CopilotAgent
@pytest.fixture
def sm():
    return WGStateMachine(sm_id="test_sm")
# 正常流程
def test_normal_flow(sm):
    sm.state = "recognizing"
    inputs = {
        "intention_type": "1",
        "serialno": "t55556",
        "site_name": "LEZ013J76S_恒大财富中心1号楼-海亮艺术华府",
        "locate_ne_name": "LEZ013J76S_恒大财富中心1号楼-海亮艺术华府",
        "province": "山东",
        "city": "济南",
        "county": "槐荫区",
        "vendor_name": "中兴",
        "occur_time": "2025-04-16 18:46:02",
        "clear_time": "",
        "alarm_title": "4G基站退服告警",
        "std_alarm_title": "基站退出服务",
        "site_type": "宏站",
        "ne_type": "ENodeB",
        "specialty1": "无线接入网",
        "network_type": "4G",
        "vip_type": "高层居民区",
        "vip_level": "普通基站",
        "room_name": "济南海亮艺术华府机房",
        "room_code_tower": "",
        "std_severity": "二级告警",
        "cover_scene": "高层居民区",
        "alarm_status": "活动",
        "locate_info": "SubNetwork=5313,ManagedElement=640809,ENBFunctionTDD=640809,EUtranCellTDD=113",
        "session_id": "bbs1234567",
        "request_id": "wwwbb2"
    }
    sm.start(inputs)
    assert sm.state == "approving"
    # 活动审批 intention_type:2
    inputs_1 = {
        "intention_type": "2",
        "serialno": "t55556",
        "scheme_id": "sch-LFE041060H-343242-07030001",
        "approval_user": "user1",
        "approval_time": "2025-04-16 18:50:02",
        "approval_result": True,
        "session_id": "bbs1234567",
        "request_id": "wwwbb2"
    }
    sm.start(inputs_1)
    # 期望状态：executing（如果审核通过）
    assert sm.state == "executing"
    inputs_2 = {
    "intention_type" : "3",
    "serialno": "t55556",
    "scheme_id": "sch-LFE041060H-2266082361-06210001",
    "execute_time": "2025-04-16 18:56:22",
    "execute_result": True,
    "session_id": "bbs1234567",
    "request_id": "wwwbb2"
}
    sm.start(inputs_2)

    assert sm.state == "evaluating"
    inputs_3 = {
        "intention_type": "4",
        "serialno": "t55556",
        "scheme_id": "sch-LFE041060H-2266082361-06210001",
        "evaluate_time": "2025-04-16 20:15:13",
        "evaluate_round": 1,
        "evaluate_result": True,
        "evaluate_indicators": ["kpi_wirelesssuccconnrate_qci1", "kpi_erabdroprate_qci1"],
        "evaluate_period_type": "min",
        "evaluate_start_time": "2025-04-16 19:00:00",
        "evaluate_end_time": "2025-04-16 20:15:00",
        "session_id": "bbs1234567",
        "request_id": "wwwbb2"
    }
    sm.start(inputs_3)
    assert sm.state == "resetting"

# 正常流程中有不通过情况
def test_normal_false_execute_flow(sm):
    sm.state = "recognizing"
    inputs = {
        "intention_type": "1",
        "serialno": "t55556",
        "site_name": "LEZ013J76S_恒大财富中心1号楼-海亮艺术华府",
        "locate_ne_name": "LEZ013J76S_恒大财富中心1号楼-海亮艺术华府",
        "province": "山东",
        "city": "济南",
        "county": "槐荫区",
        "vendor_name": "中兴",
        "occur_time": "2025-04-16 18:46:02",
        "clear_time": "",
        "alarm_title": "4G基站退服告警",
        "std_alarm_title": "基站退出服务",
        "site_type": "宏站",
        "ne_type": "ENodeB",
        "specialty1": "无线接入网",
        "network_type": "4G",
        "vip_type": "高层居民区",
        "vip_level": "普通基站",
        "room_name": "济南海亮艺术华府机房",
        "room_code_tower": "",
        "std_severity": "二级告警",
        "cover_scene": "高层居民区",
        "alarm_status": "活动",
        "locate_info": "SubNetwork=5313,ManagedElement=640809,ENBFunctionTDD=640809,EUtranCellTDD=113",
        "session_id": "bbs1234567",
        "request_id": "wwwbb2"
    }
    sm.start(inputs)
    assert sm.state == "approving"

    inputs_1 = {
        "intention_type": "2",
        "serialno": "t55556",
        "scheme_id": "sch-LFE041060H-343242-07030001",
        "approval_user": "user1",
        "approval_time": "2025-04-16 18:50:02",
        "approval_result": True,
        "session_id": "bbs1234567",
        "request_id": "wwwbb2"
    }
    sm.start(inputs_1)
    assert sm.state == "executing"

    # 执行失败 
    inputs_2 = {
    "intention_type" : "3",
    "serialno": "t55556",
    "scheme_id": "sch-LFE041060H-2266082361-06210001",
    "execute_time": "2025-04-16 18:56:22",
    "execute_result": False,
    "session_id": "bbs1234567",
    "request_id": "wwwbb2"
    }
    sm.start(inputs_2)
    assert sm.state == "interrupted"

def test_normal_false_approve_flow(sm):
    sm.state = "recognizing"
    inputs = {
        "intention_type": "1",
        "serialno": "t55556",
        "site_name": "LEZ013J76S_恒大财富中心1号楼-海亮艺术华府",
        "locate_ne_name": "LEZ013J76S_恒大财富中心1号楼-海亮艺术华府",
        "province": "山东",
        "city": "济南",
        "county": "槐荫区",
        "vendor_name": "中兴",
        "occur_time": "2025-04-16 18:46:02",
        "clear_time": "",
        "alarm_title": "4G基站退服告警",
        "std_alarm_title": "基站退出服务",
        "site_type": "宏站",
        "ne_type": "ENodeB",
        "specialty1": "无线接入网",
        "network_type": "4G",
        "vip_type": "高层居民区",
        "vip_level": "普通基站",
        "room_name": "济南海亮艺术华府机房",
        "room_code_tower": "",
        "std_severity": "二级告警",
        "cover_scene": "高层居民区",
        "alarm_status": "活动",
        "locate_info": "SubNetwork=5313,ManagedElement=640809,ENBFunctionTDD=640809,EUtranCellTDD=113",
        "session_id": "bbs1234567",
        "request_id": "wwwbb2"
    }
    sm.start(inputs)
    assert sm.state == "approving"

    # 审批不通过
    inputs_1 = {
        "intention_type": "2",
        "serialno": "t55556",
        "scheme_id": "sch-LFE041060H-343242-07030001",
        "approval_user": "user1",
        "approval_time": "2025-04-16 18:50:02",
        "approval_result": False,
        "session_id": "bbs1234567",
        "request_id": "wwwbb2"
    }
    sm.start(inputs_1)
    assert sm.state == "archived"

def test_normal_false_evaluate_flow(sm):
    sm.state = "recognizing"
    inputs = {
        "intention_type": "1",
        "serialno": "t55556",
        "site_name": "LEZ013J76S_恒大财富中心1号楼-海亮艺术华府",
        "locate_ne_name": "LEZ013J76S_恒大财富中心1号楼-海亮艺术华府",
        "province": "山东",
        "city": "济南",
        "county": "槐荫区",
        "vendor_name": "中兴",
        "occur_time": "2025-04-16 18:46:02",
        "clear_time": "",
        "alarm_title": "4G基站退服告警",
        "std_alarm_title": "基站退出服务",
        "site_type": "宏站",
        "ne_type": "ENodeB",
        "specialty1": "无线接入网",
        "network_type": "4G",
        "vip_type": "高层居民区",
        "vip_level": "普通基站",
        "room_name": "济南海亮艺术华府机房",
        "room_code_tower": "",
        "std_severity": "二级告警",
        "cover_scene": "高层居民区",
        "alarm_status": "活动",
        "locate_info": "SubNetwork=5313,ManagedElement=640809,ENBFunctionTDD=640809,EUtranCellTDD=113",
        "session_id": "bbs1234567",
        "request_id": "wwwbb2"
    }
    sm.start(inputs)
    assert sm.state == "approving"

    inputs_1 = {
        "intention_type": "2",
        "serialno": "t55556",
        "scheme_id": "sch-LFE041060H-343242-07030001",
        "approval_user": "user1",
        "approval_time": "2025-04-16 18:50:02",
        "approval_result": True,
        "session_id": "bbs1234567",
        "request_id": "wwwbb2"
    }
    sm.start(inputs_1)
    assert sm.state == "executing"

    inputs_2 = {
    "intention_type" : "3",
    "serialno": "t55556",
    "scheme_id": "sch-LFE041060H-2266082361-06210001",
    "execute_time": "2025-04-16 18:56:22",
    "execute_result": True,
    "session_id": "bbs1234567",
    "request_id": "wwwbb2"
}
    sm.start(inputs_2)
    assert sm.state == "evaluating"

    inputs_3 = {
        "intention_type": "4",
        "serialno": "t55556",
        "scheme_id": "sch-LFE041060H-2266082361-06210001",
        "evaluate_time": "2025-04-16 20:15:13",
        "evaluate_round": 1,
        "evaluate_result": False,
        "evaluate_indicators": ["kpi_wirelesssuccconnrate_qci1", "kpi_erabdroprate_qci1"],
        "evaluate_period_type": "min",
        "evaluate_start_time": "2025-04-16 19:00:00",
        "evaluate_end_time": "2025-04-16 20:15:00",
        "session_id": "bbs1234567",
        "request_id": "wwwbb2"
    }
    sm.start(inputs_3)
    assert sm.state == "interrupted"

   