# 每次运行需要修改流水号确保每次流水号不同
import pytest,json
from src.agents.wireless_guarantee_agent.wg_agent import WGAgent

def test_execute_flow():
    # 1. 构造输入
    session_id = "test_session"
    request_id = "test_request"
    question_0 = json.dumps({
        "serialno": "t12138888",
        "site_name": "LEZ013J76S_恒大财富中心1号楼-海亮艺术华府",
        "locate_ne_name": "LEZ013J76S_恒大财富中心1号楼-海亮艺术华府",
        "province": "山东",
        "city": "济南",
        "county": "槐荫区",
        "vendor_name": "中兴",
        "occur_time": "2025-04-16 18:46:02",
        "clear_time": "",
        "alarm_title": "4G基站退服告警",
        "std_alarm_title": "基站退出服务",
        "site_type": "宏站",
        "ne_type": "ENodeB",
        "specialty1": "无线接入网",
        "network_type": "4G",
        "vip_type": "高层居民区",
        "vip_level": "普通基站",
        "room_name": "济南海亮艺术华府机房",
        "room_code_tower": "",
        "std_severity": "二级告警",
        "cover_scene": "高层居民区",
        "alarm_status": "活动",
        "locate_info": "SubNetwork=5313,ManagedElement=640809,ENBFunctionTDD=640809,EUtranCellTDD=113"
    })
    tags = [json.dumps({"session_id": session_id, "request_id": request_id})]

    # 2. 实例化智能体
    wg_agent = WGAgent(session_id=session_id)

    # 3. 调用_run方法
    result_0 = wg_agent._run(
        request_id=request_id,
        question=question_0,
        tags=tags
    )
    # 4. 验证结果
    assert wg_agent.state_machine.state == "approving"
    # approving -> executing
    question_1 = json.dumps({
        "serialno": "t12138888",
        "scheme_id": "sch-LFE041060H-343242-07030001",
        "approval_user": "user1",
        "approval_time": "2025-04-16 18:50:02",
        "approval_result": True
    })
    result_1 = wg_agent._run(
        request_id=request_id,
        question=question_1,
        tags=tags
    )
    assert wg_agent.state_machine.state == "executing"
    #executing -> archived (发出clear请求)
    question_2 = json.dumps({
        "serialno": "t12138888",
        "site_name": "LEZ013J76S_恒大财富中心1号楼-海亮艺术华府",
        "locate_ne_name": "LEZ013J76S_恒大财富中心1号楼-海亮艺术华府",
        "province": "山东",
        "city": "济南",
        "county": "槐荫区",
        "vendor_name": "中兴",
        "occur_time": "2025-04-16 18:46:02",
        "clear_time": "2025-04-17 18:46:02",
        "alarm_title": "4G基站退服告警",
        "std_alarm_title": "基站退出服务",
        "site_type": "宏站",
        "ne_type": "ENodeB",
        "specialty1": "无线接入网",
        "network_type": "4G",
        "vip_type": "高层居民区",
        "vip_level": "普通基站",
        "room_name": "济南海亮艺术华府机房",
        "room_code_tower": "",
        "std_severity": "二级告警",
        "cover_scene": "高层居民区",
        "alarm_status": "已清除",
        "locate_info": "SubNetwork=5313,ManagedElement=640809,ENBFunctionTDD=640809,EUtranCellTDD=113"
    })
    result_2 = wg_agent._run(
        request_id=request_id,
        question=question_2,
        tags=tags
    )
    assert wg_agent.state_machine.state == "archived"

def test_evaluate_flow():
    # 1. 构造输入
    session_id = "test_session"
    request_id = "test_request"
    question_0 = json.dumps({
        "serialno": "t1213338",
        "site_name": "LEZ013J76S_恒大财富中心1号楼-海亮艺术华府",
        "locate_ne_name": "LEZ013J76S_恒大财富中心1号楼-海亮艺术华府",
        "province": "山东",
        "city": "济南",
        "county": "槐荫区",
        "vendor_name": "中兴",
        "occur_time": "2025-04-16 18:46:02",
        "clear_time": "",
        "alarm_title": "4G基站退服告警",
        "std_alarm_title": "基站退出服务",
        "site_type": "宏站",
        "ne_type": "ENodeB",
        "specialty1": "无线接入网",
        "network_type": "4G",
        "vip_type": "高层居民区",
        "vip_level": "普通基站",
        "room_name": "济南海亮艺术华府机房",
        "room_code_tower": "",
        "std_severity": "二级告警",
        "cover_scene": "高层居民区",
        "alarm_status": "活动",
        "locate_info": "SubNetwork=5313,ManagedElement=640809,ENBFunctionTDD=640809,EUtranCellTDD=113"
    })
    tags = [json.dumps({"session_id": session_id, "request_id": request_id})]

     # 2. 实例化智能体
    wg_agent = WGAgent(session_id=session_id)

    # 3. 调用_run方法
    result_0 = wg_agent._run(
        request_id=request_id,
        question=question_0,
        tags=tags
    )
    # 4. 验证结果
    assert wg_agent.state_machine.state == "approving"
    # approving -> executing
    question_1 = json.dumps({
        "serialno": "t1213338",
        "scheme_id": "sch-LFE041060H-343242-07030001",
        "approval_user": "user1",
        "approval_time": "2025-04-16 18:50:02",
        "approval_result": True
    })
    result_1 = wg_agent._run(
        request_id=request_id,
        question=question_1,
        tags=tags
    )
    assert wg_agent.state_machine.state == "executing"
    # executing -> evaluating
    question_2 = json.dumps({
        "serialno": "t1213338",
        "scheme_id": "sch-LFE041060H-2266082361-06210001",
        "execute_time": "2025-04-16 18:56:22",
        "execute_result": True
    })
    result_2 = wg_agent._run(
        request_id=request_id,
        question=question_2,
        tags=tags
    )
    assert wg_agent.state_machine.state == "evaluating"
    # evaluating -> resetting (发出clear请求)
    question_3 = json.dumps({
        "serialno": "t1213338",
        "site_name": "LEZ013J76S_恒大财富中心1号楼-海亮艺术华府",
        "locate_ne_name": "LEZ013J76S_恒大财富中心1号楼-海亮艺术华府",
        "province": "山东",
        "city": "济南",
        "county": "槐荫区",
        "vendor_name": "中兴",
        "occur_time": "2025-04-16 18:46:02",
        "clear_time": "2025-04-17 18:46:02",
        "alarm_title": "4G基站退服告警",
        "std_alarm_title": "基站退出服务",
        "site_type": "宏站",
        "ne_type": "ENodeB",
        "specialty1": "无线接入网",
        "network_type": "4G",
        "vip_type": "高层居民区",
        "vip_level": "普通基站",
        "room_name": "济南海亮艺术华府机房",
        "room_code_tower": "",
        "std_severity": "二级告警",
        "cover_scene": "高层居民区",
        "alarm_status": "已清除",
        "locate_info": "SubNetwork=5313,ManagedElement=640809,ENBFunctionTDD=640809,EUtranCellTDD=113"
    })
    result_3 = wg_agent._run(
        request_id=request_id,
        question=question_3,
        tags=tags
    )
    assert wg_agent.state_machine.state == "resetting"






   