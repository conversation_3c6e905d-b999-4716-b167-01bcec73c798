init:
	pip uninstall -y an-copilot pydantic
	pip install . '.[lint]' '.[test]' '.[package]' \
      --trusted-host ************ --default-timeout=600 \
	  --extra-index-url http://************:8099/nexus/repository/pypi-group/simple/ \
	  -i https://pypi.tuna.tsinghua.edu.cn/simple/

run:
	export PYTHONPATH=${PYTHONPATH}:. && python src/main.py -H 0.0.0.0 -P 5000

chat:
	export PYTHONPATH=${PYTHONPATH}:. && streamlit run src/chat.py --server.address=127.0.0.1 --server.port=5000

fmt: lint
	@black ./src ./tests
	@isort --profile black ./src ./tests
	@$(MAKE) lint

lint:
	@pflake8 ./src ./tests

coverage: lint
	@pytest --cov=src tests
