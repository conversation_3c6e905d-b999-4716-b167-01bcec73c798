## 请求数据样例
### 对话
小区GGZ010P922R1_惠尔大厦-龙泉商城如何业务保障
小区GLZ010L881R1_历下消防大队南-泉城广场如何业务保障
小区GLH0103569E1_新增微站23-历下教委如何业务保障

### JSON
单小区问题（修改时间  2024-08-04 00:00:00
2024-08-13 08:00:00
2024-08-14 10:00:00
2024-08-15 08:00:00
2024-08-16 08:00:00
2024-08-19 01:00:00
2024-08-19 02:00:00
2024-08-19 03:00:00
2024-08-19 04:00:00
2024-08-19 05:00:00
2024-08-19 06:00:00
2024-08-19 08:00:00）

```
{
  "start_time": "2024-08-04 00:00:00",
  "enodeb_id": "2558747",
  "cell_id": "9",
  "city": "济南",
  "county": "历下区",
  "cell_name": "GLH0103569E1_新增微站23-历下教委",
  "site_name": "GLH0103569E_新增微站23-历下教委",
  "net_type": "5G",
  "video_playdelay_frequency": "0",
  "video_playwait_time": "213",
  "video_playdelay_ratio": "0",
  "video_effdownload_rate": "0",
  "video_tcp_downlink_avgrtt_latency": "5",
  "video_tcp_downlink_retran_rate": "0",
  "video_tcp_connect_confir_avg_latency": "5",
  "boundary": "无线",
  "cause": "下行弱覆盖"
}
```

物点问题
```
{
  "start_time": "2024-08-13 08:00:00",
  "enodeb_id": "2556315",
  "cell_id": "6",
  "city": "济南",
  "county": "历下区",
  "cell_name": "GLZ010H726E1_泉城广场国旗西南微站1-泉城广场地下",
  "site_name": "GLZ010H72R_嘉益大厦-泉城广场地下",
  "net_type": "5G",
  "video_playdelay_frequency": "0",
  "video_playwait_time": "213",
  "video_playdelay_ratio": "0",
  "video_effdownload_rate": "0",
  "video_tcp_downlink_avgrtt_latency": "5",
  "video_tcp_downlink_retran_rate": "0",
  "video_tcp_connect_confir_avg_latency": "5",
  "boundary": "无线",
  "cause": "下行弱覆盖"
}
```

## 任务处理表
10.1.192.96:5432/intelligence
CREATE TABLE service_guarantee.tm_task_history (
	id serial4 NOT NULL, -- 主键
	start_time timestamp NULL,
	cell_name varchar NULL,
	btree_code varchar NULL, -- 行为树编码
	btree_context jsonb NULL, -- 行为树上下文
	plan_info text NULL,
	create_time timestamp DEFAULT timezone('Asia/Shanghai'::text, CURRENT_TIMESTAMP) NULL, -- 创建时间
	CONSTRAINT tm_task_history_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE service_guarantee.tm_task_history IS '任务历史表';

