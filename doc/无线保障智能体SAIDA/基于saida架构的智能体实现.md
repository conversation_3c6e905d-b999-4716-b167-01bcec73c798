
# 基于saida架构的智能体实现

+ 本文档旨在说明如何使用 saida 框架，快速构建一个具备情境感知能力与意图驱动行为能力的智能体。
+ 开发者可根据具体业务场景，定义并实现` Percept（感知）`、`Status（状态）`、`Goal（目标）` 和 `Plan（计划）`模块，以及对应的 `Processor（处理器）`逻辑。
+ 此文中示例代码以`动力风冷高温故障分析智能体`的实现为例
+ `saida` 已集成在 `an-copilot` 中，需使用版本 ≥ `2.5.0rc4`
+ 在 `an-copilot-demo` 项目中可查看  `saida-demo` 参考示例
	+ **注意**：当前`an-copilot-demo` 项目中 `an-copilot` 版本，在`pyproject.toml`中查看
---

## 一、快速开始`saida`

##### 1. 定义核心模块
+ `Percept（感知）`
	+ 表示外部输入的感知对象，负责对原始输入进行预处理或结构化转换，为后续的目标判断与规划提供基础。
+ `Status（状态）`
	+ 表示智能体当前所处的业务状态，可跨多次交互或处理过程持续记录与演化。
+ `Goal（目标）` 
	+ 由感知结果与反思逻辑共同推导出的当前意图或目标，驱动后续的计划生成与执行。
+ `Plan（计划）`
	+ 代表一个可执行的业务方案或动作序列，用于完成特定目标，是智能体最终行为的具体实现。一个具体的业务实现，代表一个执行计划
##### 2. 实现业务处理逻辑

saida 框架内置了基础的感知与决策流程，负责自动串联各模块执行流程。开发者仅需实现与业务相关的 Processor（处理器），即可完成定制化智能体行为。

##### 3. SAIDA的输入和输出

输入：
+ stimulus： 输入源对象
+ `context: PowerAlarmContext`：自定义全局上下文

输出：
+ `response: PowerAlarmResponse`：自定义返回模型

`saida`初始化及运行示例代码：

```python
saida: Saida[
        PowerAlarmState,
        AlarmGoalType,
        PowerAlarmPlanExecuteResult,
        PowerAlarmResponse,
        PowerAlarmContext,
    ] = HeartBeatComponents(
        tag="saida",
        default=Saida(
            initial_state=PowerAlarmState(),
            perception_processor=PowerAlarmPerceptionProcessor(),
            reflection_processor=PowerAlarmReflectionProcessor(),
            goal_checker_processor=PowerAlarmGoalCheckerProcessor(),
            long_term_memory_processor=PowerAlarmLongTermMemoryProcessor(
                goals=POWER_ALARM_PREDEFINED_GOALS,
                knowledge=POWER_ALARM_PREDEFINED_KNOWLEDGE,
                ontologies=POWER_ALARM_PREDEFINED_ONTOLOGY,
            ),
            planner_processor=PowerAlarmPlannerProcessor(),
            response_processor=PowerAlarmResponseProcessor(),
        ),
    )
stimulus = Stimulus.create(
	session_id=self.session_id,
	request_id=request_id,
	data=question,
	type=StimulusType.TEXT,
)
context = PowerAlarmContext(
	session_id=self.session_id,
	request_id=request_id,
	history=history,
	contexts=contexts,
	begin_time=begin_time,
	user_input=question,
)
response: PowerAlarmResponse = self.saida.stimulus(
	stimulus=stimulus, context=context
)
```


## 二、业务处理器及核心模块定义

在processor(处理器)中实现各个模块的业务实现，根据输入输出的不同，按照执行顺序有几种处理器
+ perception_processor：感知处理器
+ reflection_processor：反思处理器
+ goal_checker_processor：目标检查处理器
+ planner_processor：执行计划处理器
+ response_processor：结果处理器
下面将介绍各个处理器的作用及如何实现

---
### 1、perception_processor - 感知处理器

**主要职责**：将原始输入 Stimulus（外部刺激）解析为结构化的感知对象 Percept，为智能体后续的反思与决策提供基础信息。

在 saida 框架中，感知处理器的 process 方法由框架自动调用，其输入包括：
+ `Stimulus`
+ `Context(上下文)`

此处理器` process 方法 ` 中可实现：根据输入`Stimulus`结合自身业务，输出为的` Percept（感知）`对象

**注意**：感知处理器仅处理当前输入的 Stimulus，不建议在此阶段引入历史信息或状态判断，避免职责混乱。

`动力风冷高温故障分析智能体`中，根据具体业务定义了两种` Percept（感知）`，分别为：

+ UserInputPercept：用户输入感知对象
+ PowerAlarmPercept：动力告警感知对象

```python
class UserInputPercept(Percept):
    """用户输入感知对象"""
    user_input: str

class PowerAlarmPercept(Percept):
    """动力告警感知对象"""
    alarm_info: Optional[AlarmInfo] = None
    # AlarmInfo中为告警信息字段，字段过多，此处省略展示
```


`动力风冷高温故障分析智能体`中，实现的业务逻辑为：
+ 判断输入是json，返回`PowerAlarmPercept`，表示其为告警对象
+ 判断输入不是json，返回`UserInputPercept`，表示为用户自由问答

`PowerAlarmPerceptionProcessor`实现如下：

```python

class PowerAlarmPerceptionProcessor(
    IPerceptionProcessor[Stimulus, Percept, PowerAlarmContext]
):
    """
    自定义感知处理器，将 Stimulus 转换为业务感知对象 Percept 对象。
    这里可以根据实际业务需求扩展更多的 Percept。
    需要注意的是，这里只针对输入的 Stimulus 类型进行处理，不建议在此环节考虑历史
    """

    def process(self, stimulus: Stimulus, context: PowerAlarmContext) -> Percept:
        ces_stream_sender = CesStreamSender(settings.ces_stream)
        if stimulus.type is StimulusType.TEXT:
            try:
                alarm_info = AlarmInfo.model_validate_json(stimulus.data)
                context.alarm_info = alarm_info
                ces_stream_sender.send_packaged_answer(
                    session_id=context.session_id,
                    request_id=context.request_id,
                    message="业务感知：动力告警感知对象",
                    begin_time=context.begin_time,
                )
                return PowerAlarmPercept(alarm_info=alarm_info)
            except ValidationError as e:
                an_logger.debug(str(e))
                ces_stream_sender.send_packaged_answer(
                    session_id=context.session_id,
                    request_id=context.request_id,
                    message="业务感知：用户输入感知对象",
                    begin_time=context.begin_time,
                )
                return UserInputPercept(
                    user_input=stimulus.data,
                    source_stimulus=stimulus,
                )
        elif stimulus.type is StimulusType.JSON:
            alarm_info = AlarmInfo.model_validate_json(stimulus.data)
            context.alarm_info = alarm_info
            ces_stream_sender.send_packaged_answer(
                session_id=context.session_id,
                request_id=context.request_id,
                message="业务感知：动力告警感知对象",
                begin_time=context.begin_time,
            )
            return PowerAlarmPercept(alarm_info=alarm_info)
        else:
            raise ValueError(f"Unsupported stimulus type: {stimulus.type}")

```



### 2、reflection_processor - 反思处理器

**主要职责**：基于当前感知（Percept）、历史状态、历史感知以及上下文，推导出新的业务状态（State），体现智能体对信息的“理解”与“反应”。

在 saida 框架中，反思处理器中的 process 方法由框架自动调用，输入包括：
+ `percept（感知）`
+ `percept_history（历史感知）`
+ `state（历史状态）`
+ `Context(上下文)`
`这些参数均由 saida 框架自动传入，开发者只需关注具体业务逻辑的实现。`

反思处理器` process 方法` 中需结合业务规则，在内部生成并返回新的 ` State（状态）` 对象。

此处理器中可实现
+ 1. 根据`percept（感知）`内容，意图分类后，返回此次新` State（状态）`
+ 2. 结合输入`percept_history（历史感知）`，实现多轮意图分类
+ 3. 结合输入`state（历史状态）`，实现多轮状态判断

`动力风冷高温故障分析智能体`中，根据具体业务定义了多种` State（状态）`，分别为：

```python
class AlarmStateEnum(StrEnum):
    IDLE = "空闲状态"  # 空闲状态
    ALARM_ANALYSIS_PENDING = "告警分析待处理"  # 告警分析待处理
    SUGGESTION_PUSHED = "处理建议已推送"  # 处理建议已推送
    REGENERATE_SUGGESTION = "重新生成处理建议"  # 重新生成处理建议
    ALARM_INFO_QUERY = "告警信息查询待处理"  # 告警信息查询
    ARCHIVING = "归档待处理"  # 归档待处理
    CLOSED = "已关闭"  # 已关闭
    REJECTED = "拒绝处理"  # 拒绝处理
class PowerAlarmState(State):
    state: AlarmStateEnum = AlarmStateEnum.IDLE
    message: Optional[str] = None
```

`动力风冷高温故障分析智能体`中，实现的业务逻辑为：
+ 判断感知对象类型，返回新状态
	+ PowerAlarmPercept，返回`ALARM_ANALYSIS_PENDING = "告警分析待处理"`状态
	+ UserInputPercept，执行用户输入意图分类，根据意图分类结果返回不同状态
		+ 方案追问，返回` REGENERATE_SUGGESTION = "重新生成处理建议" `状态
		+ 状态确认，返回` ALARM_INFO_QUERY = "告警信息查询待处理" `状态
		+ 信息查询，返回` ALARM_INFO_QUERY = "告警信息查询待处理" 状态`
		+ 告警归档，返回` ARCHIVING = "归档待处理" 状态`
		+ 其他类，返回` REJECTED = "拒绝处理"状态`
+ 结合上轮状态和新意图，返回新的状态，实现状态机的效果
	+ 1. 输入状态是`告警分析待处理`，当前状态不是`IDLE = "空闲状态"`，返回`拒绝处理`状态
	+ 2. 输入状态是`告警分析待处理`，当前状态不是`处理建议已推送`，返回`拒绝处理`状态
	+ ....

`PowerAlarmReflectionProcessor`实现如下：

```python


class PowerAlarmReflectionProcessor(
    IReflectionProcessor[Percept, PowerAlarmState, PowerAlarmContext]
):
    """
    自定义反思处理器，将 Percept 对象转换为业务状态 PowerAlarmState 对象。
    这里可以根据实际业务需求扩展更多的状态处理逻辑。
    """

    def process(
        self,
        percept: Percept,
        percept_history: Optional[List[Percept]],
        state: PowerAlarmState,
        context: PowerAlarmContext,
    ) -> PowerAlarmState:
        """
        根据当前感知 percept，当前状态 state，历史感知 percept_history，生成新的业务状态 ComplaintState。
        """
        context.plan_success_state = state.state
        if isinstance(percept, UserInputPercept):
            if not percept.user_input:
                return self.build_alarm_state(
                    context, percept, AlarmStateEnum.REJECTED, message="用户输入不能为空"
                )
            else:
				return self.build_alarm_state(
					context,
					percept,
					AlarmStateEnum.REJECTED,
					message="我不能回答此问题，可以让我重新生成处理方案，查询告警是否恢复，或者归档此告警",
				)
        elif isinstance(percept, PowerAlarmPercept):
            if state.state == AlarmStateEnum.CLOSED:
                # 初始状态，直接进入告警分析待处理状态
                return self.build_alarm_state(
                    context, percept, AlarmStateEnum.REJECTED, "告警已关闭，无法处理新的输入"
                )
            if state.state == AlarmStateEnum.IDLE:
                # 空闲状态，直接进入告警分析待处理状态
                return self.build_alarm_state(
                    context, percept, AlarmStateEnum.ALARM_ANALYSIS_PENDING
                )
            else:
                return self.build_alarm_state(
                    context,
                    percept,
                    AlarmStateEnum.REJECTED,
                    "此会话已经在处理中，请等待处理完成。\n你可以输入新的问题，让我重新生成处理方案，查询告警是否恢复，或者归档此告警",
                )
        else:
            raise ValueError(f"Unsupported percept type: {type(percept)}")

    def build_alarm_state(
        self, context: PowerAlarmContext, percept, state: AlarmStateEnum, message=""
    ):
        ces_stream_sender = CesStreamSender(settings.ces_stream)
        ces_stream_sender.send_packaged_answer(
            session_id=context.session_id,
            request_id=context.request_id,
            message=f"感知反思状态：{state.value}",
            begin_time=context.begin_time,
        )
        return PowerAlarmState(percept=percept, state=state, message=message)

```


### 3、goal_checker_processor - 目标检查处理器

**主要职责**：目标检查处理器用于根据当前状态（State）从候选目标列表中选出最适合的目标（Goal），驱动后续的计划生成与执行。可根据不同状态下的业务需求，制定目标筛选与优先级策略。

在 saida 框架中，该处理器的 process 方法由框架自动调用，其输入包括：
+ ` State（状态）`
+ ` Goals（目标列表）` 
+ ` Context(上下文)`

处理器` process 方法` 中需根据状态判断逻辑，从 goals 列表中筛选出最匹配的目标对象，并返回` Goal（目标）`

该处理器是连接感知结果与意图选择的关键环节，适合在此实现目标推理、目标匹配、优先级排序、模型参与决策等智能行为逻辑。  
+ 根据输入` State（状态）`，选择最合适的` Goal（目标）`，
+ 例：根据输入，选择合适的 `Goal(行为树)`

`动力风冷高温故障分析智能体`中，根据具体业务定义了多种` Goal（目标）`，分别为：

```python

class AlarmAnalysisGoal(Goal):
    """执行水冷高温告警处置方案"""
    pass

class RegenerateSuggestionGoal(Goal):
    """制定处理方案"""
    pass

class AlarmInfoQueryGoal(Goal):
    """查询告警信息"""
    pass


class AlarmArchivingGoal(Goal):
    """告警归档目标"""
    pass

class UnknownGoal(Goal):
    """无效目标"""
    pass

AlarmGoalType = Union[
    UnknownGoal,
    AlarmAnalysisGoal,
    RegenerateSuggestionGoal,
    AlarmInfoQueryGoal,
    AlarmArchivingGoal,
]

```

`动力风冷高温故障分析智能体`中，实现的业务逻辑为：
+ 根据状态选择对应的Goal
+ 动力告警智能体状态与Goal一一对象，所以选择逻辑为判断后直接返回对应的Goal对象

`PowerAlarmGoalCheckerProcessor`实现如下：

```python

class PowerAlarmGoalCheckerProcessor(
    IGoalCheckerProcessor[PowerAlarmState, AlarmGoalType, PowerAlarmContext]
):
    """自定义目标适用性检查器处理器，检查给定的状态和目标列表，返回适用的目标。"""

    def process(
        self,
        state: PowerAlarmState,
        goals: List[AlarmGoalType],
        context: PowerAlarmContext,
    ) -> AlarmGoalType | None:
        if state.state == AlarmStateEnum.ALARM_ANALYSIS_PENDING:
            if isinstance(state.percept, PowerAlarmPercept):
                # 投诉单采用行为树策略，根据投诉单信息分析 Goal（可以使用模型分析）
                # _complaint_id = state.percept.complaint_id  # noqa: F841
                # _service_code = state.percept.service_code  # noqa: F841

                # 假设我们有一些预定义的目标
                alarm_analysis_goal = [
                    g for g in goals if isinstance(g, AlarmAnalysisGoal)
                ]
                if not alarm_analysis_goal:
                    return None

                # 如果分析会多个 Goal，返回优先级别高的
                return max(alarm_analysis_goal, key=lambda g: g.priority)
            else:
                return None
        elif state.state == AlarmStateEnum.REGENERATE_SUGGESTION:
            # 查找数据查询目标
            regenerate_suggestion_goal = [
                g for g in goals if isinstance(g, RegenerateSuggestionGoal)
            ]

            if not regenerate_suggestion_goal:
                return None

            # 从多个数据查询目标中返回一个
            if len(regenerate_suggestion_goal) == 1:
                return regenerate_suggestion_goal[0]
            else:
                # TODO: 取优先级最高的或让大模型选择一个
                return max(regenerate_suggestion_goal, key=lambda g: g.priority)
        elif state.state == AlarmStateEnum.ALARM_INFO_QUERY:
            # 知识问答目标
            alarm_info_query_goal = [
                g for g in goals if isinstance(g, AlarmInfoQueryGoal)
            ]
            if not alarm_info_query_goal:
                return None

            # 从多个知识问答目标中返回一个
            if len(alarm_info_query_goal) == 1:
                return alarm_info_query_goal[0]
            else:
                # TODO: 取优先级最高的或让大模型选择一个
                return max(alarm_info_query_goal, key=lambda g: g.priority)
        elif state.state == AlarmStateEnum.ARCHIVING:
            # 知识问答目标
            archiving_goal = [g for g in goals if isinstance(g, AlarmArchivingGoal)]
            if not archiving_goal:
                return None

            # 从多个知识问答目标中返回一个
            if len(archiving_goal) == 1:
                return archiving_goal[0]
            else:
                # TODO: 取优先级最高的或让大模型选择一个
                return max(archiving_goal, key=lambda g: g.priority)
        elif state.state == AlarmStateEnum.REJECTED:
            return UnknownGoal(
                id="unknown",
                name="未知目标",
                description=state.message or "无法处理的目标",
                priority=100,
            )
        else:
            raise ValueError(f"Unsupported state: {state}")


```


### 4、planner_processor - 执行计划处理器

**主要职责**：执行计划处理器用于根据当前状态（State）和匹配到的目标（Goal），生成对应的可执行计划（Plan），是智能体从“意图”走向“行动”的关键步骤。

在 saida 框架中，该处理器的 process 方法由框架自动调用，其输入包括：
+ ` State（状态）`
+ ` Goal（目标）` 
+ ` Context(上下文)`，输出为` Plan（计划）`

处理器 `process 方法`中自己实现根据目标类型，构造并返回一个具体的` Plan（计划）`

`动力风冷高温故障分析智能体`中，根据具体业务定义了多种` Plan（计划）`，分别为：

```python


class AlarmAnalysisPlan(
    Plan[AlarmAnalysisGoal, PowerAlarmContext, PowerAlarmPlanExecuteResult]
):
    """告警分析计划"""

    def execute(self, context: PowerAlarmContext) -> PowerAlarmPlanExecuteResult:
        # 实际告警分析业务逻辑执行，代码过程，此处省略
        return PowerAlarmPlanExecuteResult(
            session_id=context.session_id,
            request_id=context.request_id,
            contexts=context.contexts,
            agent_output=context.agent_output,
            new_state=PowerAlarmState(
                percept=None,
                state=AlarmStateEnum.SUGGESTION_PUSHED,
                message="处理计划已推送, 等待反馈",
            ),
        )


class RegenerateSuggestionPlan(
    Plan[RegenerateSuggestionGoal, PowerAlarmContext, PowerAlarmPlanExecuteResult]
):
    """处理建议生成计划"""

    def execute(self, context: PowerAlarmContext) -> PowerAlarmPlanExecuteResult:
        # 业务逻辑过程，此处省略
        return PowerAlarmPlanExecuteResult(
            session_id=context.session_id,
            request_id=context.request_id,
            contexts=context.contexts,
            agent_output=context.agent_output,
            new_state=PowerAlarmState(
                percept=None,
                state=AlarmStateEnum.SUGGESTION_PUSHED,
                message="已重新生成处理计划",
            ),
        )


class AlarmInfoQueryPlan(
    Plan[AlarmInfoQueryGoal, PowerAlarmContext, PowerAlarmPlanExecuteResult]
):
    """告警信息查询计划"""

    def execute(self, context: PowerAlarmContext) -> PowerAlarmPlanExecuteResult:
        # 业务逻辑过程，此处省略
        return PowerAlarmPlanExecuteResult(
            session_id=self.context.session_id,
            request_id=self.context.request_id,
            contexts=self.context.contexts,
            agent_output=self.context.agent_output,
            new_state=new_state,
        )

class AlarmArchivingPlan(
    Plan[AlarmArchivingGoal, PowerAlarmContext, PowerAlarmPlanExecuteResult]
):
    """告警归档计划"""

    def execute(self, context: PowerAlarmContext) -> PowerAlarmPlanExecuteResult:
        # 业务逻辑过程，此处省略
        return PowerAlarmPlanExecuteResult(
            session_id=self.context.session_id,
            request_id=self.context.request_id,
            contexts=self.context.contexts,
            agent_output=self.context.agent_output,
            new_state=PowerAlarmState(
                percept=None,
                state=AlarmStateEnum.CLOSED,
                message="已归档",
            ),
        )


class UnknownPlan(Plan[UnknownGoal, PowerAlarmContext,PowerAlarmPlanExecuteResult]):
    """未知计划"""

    def execute(self, context: PowerAlarmContext) -> PowerAlarmPlanExecuteResult:
        # 业务逻辑过程，此处省略
        return PowerAlarmPlanExecuteResult(
            session_id=self.context.session_id,
            request_id=self.context.request_id,
            contexts=self.context.contexts,
            agent_output=self.context.agent_output,
            new_state=new_state,
        )


PowerAlarmPlanType = Union[
    AlarmAnalysisPlan,
    RegenerateSuggestionPlan,
    AlarmInfoQueryPlan,
    AlarmArchivingPlan,
    UnknownPlan,
]

```

`动力风冷高温故障分析智能体`中，实现的业务逻辑为：
+ 根据输入的Goal，选择合适的Plan执行计划
+ 动力告警中Goal与Plan一一对应，按照Goal的类型，返回对应的Plan

`PowerAlarmPlannerProcessor`实现如下：

```python

class PowerAlarmPlannerProcessor(
    IPlannerProcessor[
        PowerAlarmState, AlarmGoalType, PowerAlarmPlanType, PowerAlarmContext
    ]
):
    """根据投诉处理目标生成计划的处理器"""

    def process(
        self, state: PowerAlarmState, goal: AlarmGoalType, context: PowerAlarmContext
    ) -> PowerAlarmPlanType:
        """
        根据目标生成计划。
        这里可以根据实际业务需求扩展更多的计划生成逻辑。
        """
        if isinstance(goal, AlarmAnalysisGoal):
            alarm_info = context.alarm_info
            context_alarm_info = alarm_info.json()
            context.contexts["context_alarm_info"] = context_alarm_info
        else:
            context_alarm_info = context.contexts.get("context_alarm_info", "")
            if not context_alarm_info:
                context.alarm_info = None
            else:
                alarm_info: AlarmInfo = AlarmInfo.model_validate_json(
                    context_alarm_info
                )
                context.alarm_info = alarm_info
        if isinstance(goal, AlarmAnalysisGoal):
            return AlarmAnalysisPlan(
                name=f"计划_{goal.name}", description="", goal=goal, context=context
            )
        elif isinstance(goal, RegenerateSuggestionGoal):
            return RegenerateSuggestionPlan(
                name=f"计划_{goal.name}", description="", goal=goal, context=context
            )
        elif isinstance(goal, AlarmInfoQueryGoal):
            return AlarmInfoQueryPlan(
                name=f"计划_{goal.name}", description="", goal=goal, context=context
            )
        elif isinstance(goal, AlarmArchivingGoal):
            return AlarmArchivingPlan(
                name=f"计划_{goal.name}", description="", goal=goal, context=context
            )
        elif isinstance(goal, UnknownGoal):
            # 未知目标，返回一个默认的计划
            return UnknownPlan(
                name="未知目标计划",
                description="无法处理的目标",
                goal=goal,
                context=context,
            )
        else:
            raise ValueError(f"Unsupported goal type: {type(goal)}")

```



### 5、response_processor - 返回处理器

**主要职责**：返回处理器负责根据执行计划的结果（PlanResult）与上下文信息，生成智能体最终的响应输出（Response）。它是智能体与外部交互的最后一步。

在 saida 框架中，process 方法由框架自动调用，其输入包括：
+ ` PlanResult（Plan执行结果）` ：执行计划后的结果数据，包含模型输出、中间状态等内容
+ ` Context (上下文)`：上下文信息，记录整个会话中累积的数据

处理器 `process 方法` 中需根据 ` PlanResult（Plan执行结果）` 和 ` Context (上下文)` 中的信息，构造并返回最终响应对象 Response，作为`saida`对外的输出。

`动力风冷高温故障分析智能体`中，根据具体业务定义了`Response（最终返回）`，为：

```python
class PowerAlarmResponse(Response):
    session_id: str
    request_id: str
    contexts: Optional[dict] = Field(default_factory=dict)
    agent_output: Optional[str] = None

```

`动力风冷高温故障分析智能体`中，实现的业务逻辑为：
+ 将数据组装为` Response（最终返回）`

`PowerAlarmResponseProcessor`实现如下：

```python

class PowerAlarmResponseProcessor(
    IResponseProcessor[
        PowerAlarmPlanExecuteResult, PowerAlarmResponse, PowerAlarmContext
    ]
):
    def process(
        self,
        plan_execute_result: PowerAlarmPlanExecuteResult,
        context: PowerAlarmContext,
    ) -> PowerAlarmResponse:
        return PowerAlarmResponse(
            session_id=context.session_id,
            request_id=context.request_id,
            contexts=context.contexts,
            agent_output=context.agent_output,
        )


```

## 三、Saida扩展定义

通过与CES的心跳，可以将此智能体的saida架构展示到An-Evo平台应用列表

定义以下基本信息，在构造SAIDA时传入

```python
from an_copilot.framework.saida.core.models.models import GoalType, Knowledge

from src.agents.an_copilot_power_alarm_analysis.saida_model import (
    AlarmAnalysisGoal,
    AlarmArchivingGoal,
    AlarmInfoQueryGoal,
    PowerAlarmOntology,
    RegenerateSuggestionGoal,
)

# 预定义本体列表
POWER_ALARM_PREDEFINED_ONTOLOGY = [
    PowerAlarmOntology(
        id="ontology_1",
        name="告警本体1",
        description="告警相关的本体定义",
        owl="""
<rdf:RDF>
  <rdf:Description rdf:about="http://example.org/books/123">
    <dc:title>动力告警分析</dc:title>
    <dc:creator>动力告警分析</dc:creator>
    <dc:date>2025-07-05</dc:date>
  </rdf:Description>
</rdf:RDF>
        """,
    ),
]

# 预定义知识列表
POWER_ALARM_PREDEFINED_KNOWLEDGE = [
    Knowledge(id="complaint_knowledge_1", name="告警处理知识库"),
]

# 预定义目标列表
POWER_ALARM_PREDEFINED_GOALS = [
    AlarmAnalysisGoal(
        id="9ec6b69395bf4bdcba6170ebd0e49224",
        name="执行水冷高温告警处置方案",
        description="执行水冷高温告警处置方案",
        type=GoalType.BTREE,
    ),
    RegenerateSuggestionGoal(
        id="regenerate_suggestion_goal",
        name="制定处理方案",
        description="制定处理方案",
    ),
    AlarmInfoQueryGoal(
        id="alarm_info_query_goal",
        name="查询告警信息",
        description="查询告警信息",
    ),
    AlarmArchivingGoal(
        id="alarm_archiving_goal",
        name="告警归档目标",
        description="告警归档目标",
    ),
]

```


## 四、推荐项目结构


```
根目录
└── src/
    └── agents/
	    ├── an_copilot_power_alarm_analysis/
		│	├── chains/
		│	│	├── __init__.py
		│	│	└── intent_analysis_chain.py
		│	├── processors/
		│	│	├── __init__.py
		│	│	├── goal_checker_processor.py
		│	│	├── long_term_memory_processor.py
		│	│	├── reflection_processor.py
		│	│	├── planner_processor.py
		│	│	├── response_processor.py
		│	│	└── perception_processor.py
		│	├── power_alarm_analysis_agent.py
		│	├── saida_define.py
		│	├── saida_model.py
		│	└── saida_plan.py
		└── __init__.py
```

