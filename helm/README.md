# WG-Copilot-GPT Helm Chart

这是一个用于部署无线保障智能体系统的 Helm Chart，包含三个微服务：

## 架构概览

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │  Post-Processor  │    │  Copilot-GPT    │
│   (Nginx)       │    │   (Java API)     │    │  (Python API)   │
│   Port: 80      │    │   Port: 9502     │    │   Port: 5000    │
│   NodePort:30500│◄───┤   ClusterIP      │    │   ClusterIP     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
        │                        │                        │
        │                        │                        │
    对外访问入口              内部API服务              内部API服务
```

## 服务说明

### 1. Frontend (wg-agent-frontend)
- **功能**: 前端界面，基于 Nginx
- **镜像**: `harbor.com/airan/wg-agent-frontend:1.0.0`
- **端口**: 80 (内部) → 30500 (NodePort，对外暴露)
- **访问**: 通过 NodePort 30500 对外提供服务

### 2. Post-Processor (Java 后端)
- **功能**: Java API 服务，处理业务逻辑
- **镜像**: `harbor.com/airan/post-processor:1.0.0`
- **端口**: 9502 (ClusterIP，内部访问)
- **依赖**: Nacos、数据库等外部服务

### 3. Copilot-GPT (Python API)
- **功能**: Python API 服务，AI 相关功能
- **镜像**: `harbor.com/airan/wg-copilot-gpt_x86_64:1.0.0`
- **端口**: 5000 (ClusterIP，内部访问)

## 部署前配置

### 必须配置的环境变量

在部署前，请根据实际环境修改 `values.yaml` 中的以下配置：

#### Python API 服务 (copilot-gpt) 环境变量：

```yaml
env:
  copilotGpt:
    # 模型地址配置 - 请根据实际环境填写
    DEFAULT_API_BASE: "https://api.siliconflow.cn/v1"
    DEFAULT_API_KEY: "your-api-key"
    QWEN_72B_MODEL_NAME: "Qwen/Qwen2.5-72B-Instruct"
    LLM_ID: "qwen-72b"
    
    # CES 服务地址配置 - 请根据实际环境填写
    CES_ENABLED: "false"  # 或 "true"
    CES_URI: "http://your-ces-server:6066"
    CES_HEARTBEAT_SOURCE_HOST: "your-host-ip"
    CES_HEARTBEAT_SOURCE_PORT: "35000"
    
    # 开启 SWAGGER
    SWAGGER_UI_ENABLED: "true"
```

#### Java 后端服务 (post-processor) 环境变量：

```yaml
env:
  postProcessor:
    # 系统组件版本
    PRE_VERSION: "1.0.0"
    GATEWAY_VERSION: "1.0.0"
    
    # JVM 配置
    JAVA_OPTS: "-Xms512m -Xmx1024m"
    
    # Nacos 配置 - 请根据实际环境填写
    NACOS_ADDR: "your-nacos-server:8848"
    NACOS_NAMESPACE: "your-namespace"
    NACOS_USERNAME: "nacos"
    NACOS_PASSWORD: "nacos"
    
    # PostgreSQL 数据库配置 - 请根据实际环境填写
    PG_HOST: "your-postgres-server"
    PG_PORT: "5432"
    PG_USERNAME: "postgres"
    PG_PASSWORD: "your-password"
    PG_DBNAME: "netoptdatabase"
    PG_DBNAME1: "complaint_agent"
    PG_DBNAME2: "agent_db"
    
    # Redis 配置 - 请根据实际环境填写
    REDIS_HOST: "your-redis-server"
    REDIS_PORT: "6379"
    REDIS_PASSWORD: "your-redis-password"
    
    # MinIO 配置 - 请根据实际环境填写
    MINIO_HOST: "your-minio-server"
    MINIO_PORT: "9000"
    MINIO_USERNAME: "admin"
    MINIO_PASSWORD: "your-minio-password"
    
    # Kafka 配置 - 请根据实际环境填写
    # 格式：ip1:port1,ip2:port2...
    KAFKA_SERVERS: "your-kafka-server1:9092,your-kafka-server2:9092"
    
    # Spring 配置
    SPRING_PROFILES_ACTIVE: "prod"  # 或其他环境
```

### 可选配置

#### 资源限制调整：
```yaml
resources:
  postProcessor:
    limits:
      cpu: 2000m      # 根据实际需求调整
      memory: 4Gi     # 根据实际需求调整
    requests:
      cpu: 500m
      memory: 1Gi
```

#### 镜像配置：
```yaml
images:
  postProcessor:
    repository: harbor.com/airan/post-processor
    tag: "1.0.0"     # 根据实际版本调整
```

## 部署命令

```bash
# 1. 修改 values.yaml 中的环境变量配置

# 2. 部署到 Kubernetes
helm install wg-copilot-gpt ./helm

# 3. 或者升级现有部署
helm upgrade wg-copilot-gpt ./helm

# 4. 查看部署状态
kubectl get pods -l app.kubernetes.io/name=wg-copilot-gpt
kubectl get services -l app.kubernetes.io/name=wg-copilot-gpt
```

## 访问方式

部署成功后，可以通过以下方式访问：

- **前端界面**: `http://<节点IP>:30500`
- **内部服务**: 通过 Kubernetes DNS 访问
  - Post-Processor: `wg-copilot-gpt-post-processor-svc:9502`
  - Copilot-GPT: `wg-copilot-gpt-copilot-gpt-svc:5000`

## 故障排查

### 查看 Pod 状态
```bash
kubectl get pods -l app.kubernetes.io/name=wg-copilot-gpt
```

### 查看 Pod 日志
```bash
# 查看前端服务日志
kubectl logs -l app.kubernetes.io/component=frontend

# 查看 Java 后端服务日志
kubectl logs -l app.kubernetes.io/component=post-processor

# 查看 Python API 服务日志
kubectl logs -l app.kubernetes.io/component=copilot-gpt
```

### 查看服务状态
```bash
kubectl get services -l app.kubernetes.io/name=wg-copilot-gpt
```

### 外部依赖服务检查

在部署前，请确保以下外部服务可正常访问：

1. **Nacos 服务注册中心**
   - 地址: `NACOS_ADDR` 配置的地址
   - 端口: 通常为 8848
   - 验证: `curl http://your-nacos-server:8848/nacos/`

2. **PostgreSQL 数据库**
   - 地址: `PG_HOST` 配置的地址
   - 端口: `PG_PORT` 配置的端口
   - 验证: `telnet your-postgres-server 5432`

3. **Redis 缓存服务**
   - 地址: `REDIS_HOST` 配置的地址
   - 端口: `REDIS_PORT` 配置的端口
   - 验证: `redis-cli -h your-redis-server -p 6379 ping`

4. **MinIO 对象存储**
   - 地址: `MINIO_HOST` 配置的地址
   - 端口: `MINIO_PORT` 配置的端口
   - 验证: `curl http://your-minio-server:9000/minio/health/live`

5. **Kafka 消息队列**
   - 地址: `KAFKA_SERVERS` 配置的地址
   - 端口: 通常为 9092
   - 验证: `telnet your-kafka-server 9092`

### 常见问题

1. **Java 服务启动失败**
   - 检查 Nacos 连接配置
   - 检查 PostgreSQL 数据库连接配置
   - 检查 Redis 连接配置
   - 检查 MinIO 和 Kafka 连接配置
   - 查看 Pod 日志确认具体错误

2. **前端无法访问后端 API**
   - 确认 post-processor 服务正常运行
   - 检查 Kubernetes DNS 解析
   - 确认 API_AGENT_URL 环境变量设置正确

3. **NodePort 无法访问**
   - 确认防火墙设置
   - 确认 NodePort 端口 30500 未被占用
   - 检查 Kubernetes 节点网络配置

4. **Redis 连接问题**
   - 确认 Redis 服务可达性
   - 检查 Redis 密码配置
   - 验证网络策略是否允许访问

5. **数据库连接问题**
   - 确认 PostgreSQL 服务可达性
   - 检查数据库用户权限
   - 验证数据库名称是否存在

## 卸载

```bash
helm uninstall wg-copilot-gpt
```

## 注意事项

1. **环境变量配置**: 部署前必须根据实际环境配置 Java 后端的环境变量
2. **资源配置**: 根据实际负载调整各服务的资源限制
3. **网络策略**: 如果集群启用了网络策略，需要配置相应的网络访问规则
4. **存储**: 如果服务需要持久化存储，需要额外配置 PVC