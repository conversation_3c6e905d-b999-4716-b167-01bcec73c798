# Default values for wg-copilot-gpt.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

# NodePort端口配置 - PaaS平台使用
port: 30500

nameOverride: wg-copilot-gpt

# 镜像配置
images:
  # Python API 服务
  copilotGpt:
    repository: harbor.com/airan/wg-copilot-gpt_x86_64
    tag: "1.0.0"
    pullPolicy: IfNotPresent

  # Java 后端服务
  postProcessor:
    repository: harbor.com/airan/post-processor
    tag: "1.0.0"
    pullPolicy: IfNotPresent

  # 前端服务
  frontend:
    repository: harbor.com/airan/wg-agent-frontend
    tag: "1.0.0"
    pullPolicy: IfNotPresent

  # 镜像拉取Secret（如果需要）
  pullSecrets: []
  #   - name: harbor-registry-secret

# Portal配置 - PaaS平台入口URL（前端页面入口）
portal:
  enabled: true
  # 这个URL会在部署时被PaaS平台自动替换为实际的访问地址
  # 格式: http://<service-name>:<nodeport> 或 https://<domain>/<path>
  url: ""
  name: "WG-Agent Frontend"
  description: "无线保障智能体前端界面"

# 服务配置
services:
  # 前端服务（对外暴露）
  frontend:
    type: NodePort
    port: 80
    nodePort: 30500
  
  # Python API 服务（内部访问）
  copilotGpt:
    type: ClusterIP
    port: 5000
  
  # Java 后端服务（内部访问）
  postProcessor:
    type: ClusterIP
    port: 9502

# 环境变量配置
env:
  # Python API 服务环境变量
  copilotGpt:
    PYTHONPATH: "."
    
    # 模型地址配置 - 用户需要根据实际环境填写
    DEFAULT_API_BASE: "https://api.siliconflow.cn/v1"
    DEFAULT_API_KEY: "sk-vqtpsvxmjaknlozrsrxeaanqdxstsiehdpharxgzsbcuifcj"
    QWEN_72B_MODEL_NAME: "Qwen/Qwen2.5-72B-Instruct"
    LLM_ID: "qwen-72b"
    
    # CES 服务地址配置 - 用户需要根据实际环境填写
    CES_ENABLED: "false"
    CES_URI: "http://************:6066"
    CES_HEARTBEAT_SOURCE_HOST: "************"
    CES_HEARTBEAT_SOURCE_PORT: "5000"
    
    # 开启 SWAGGER
    SWAGGER_UI_ENABLED: "false"
  
  # Java 后端服务环境变量（用户需要手动填写）
  postProcessor:    
    # Nacos 配置 - 用户需要根据实际环境填写
    NACOS_ADDR: "************:8848"
    NACOS_NAMESPACE: "complaint-agent-dev"
    NACOS_USERNAME: "nacos"
    NACOS_PASSWORD: "nacos"
    
    # PostgreSQL 数据库配置 - 用户需要根据实际环境填写
    PG_HOST: "***********"
    PG_PORT: "54321"
    PG_USERNAME: "postgres"
    PG_PASSWORD: "retina"
    PG_DBNAME: "netoptdatabase"
    PG_DBNAME1: "complaint_agent"
    PG_DBNAME2: "agent_db"
    
    # Redis 配置 - 用户需要根据实际环境填写
    REDIS_HOST: "***********"
    REDIS_PORT: "6379"
    REDIS_PASSWORD: "retina"
    
    # Spring 配置
    SPRING_PROFILES_ACTIVE: "dev"
  
  # 前端服务环境变量
  frontend:
    # API_AGENT_URL 将自动设置为 post-processor 服务的内部地址

# 安全上下文配置
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  runAsGroup: 3000
  fsGroup: 2000
  capabilities:
    drop:
      - ALL
  readOnlyRootFilesystem: false
  allowPrivilegeEscalation: false

# 资源限制配置
resources:
  # Python API 服务资源配置
  copilotGpt:
    limits:
      cpu: 1000m
      memory: 2Gi
    requests:
      cpu: 250m
      memory: 512Mi
  
  # Java 后端服务资源配置
  postProcessor:
    limits:
      cpu: 2000m
      memory: 4Gi
    requests:
      cpu: 500m
      memory: 1Gi
  
  # 前端服务资源配置
  frontend:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 128Mi

