# Python API 服务 Service (内部访问)
apiVersion: v1
kind: Service
metadata:
  name: {{ include "wg-copilot-gpt.name" . }}-copilot-gpt-svc
  labels:
    {{- include "wg-copilot-gpt.labels" . | nindent 4 }}
    app.kubernetes.io/component: copilot-gpt
spec:
  type: {{ .Values.services.copilotGpt.type }}
  ports:
    - port: {{ .Values.services.copilotGpt.port }}
      targetPort: {{ .Values.services.copilotGpt.port }}
      protocol: TCP
      name: http
  selector:
    {{- include "wg-copilot-gpt.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: copilot-gpt

---
# Java 后端服务 Service (内部访问)
apiVersion: v1
kind: Service
metadata:
  name: {{ include "wg-copilot-gpt.name" . }}-post-processor-svc
  labels:
    {{- include "wg-copilot-gpt.labels" . | nindent 4 }}
    app.kubernetes.io/component: post-processor
spec:
  type: {{ .Values.services.postProcessor.type }}
  ports:
    - port: {{ .Values.services.postProcessor.port }}
      targetPort: {{ .Values.services.postProcessor.port }}
      protocol: TCP
      name: http
  selector:
    {{- include "wg-copilot-gpt.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: post-processor

---
# 前端服务 Service (对外暴露)
apiVersion: v1
kind: Service
metadata:
  name: {{ include "wg-copilot-gpt.name" . }}-frontend-svc
  labels:
    {{- include "wg-copilot-gpt.labels" . | nindent 4 }}
    app.kubernetes.io/component: frontend
  annotations:
    {{- if .Values.portal.enabled }}
    # PaaS平台portal配置 - 用于显示应用入口
    {{- if .Values.portal.url }}
    portal.paas/url: {{ .Values.portal.url | quote }}
    {{- else }}
    # 默认使用NodePort访问方式，部署时需要替换为实际的外部访问地址
    portal.paas/url: "http://{{ include "wg-copilot-gpt.name" . }}-frontend-svc:{{ .Values.services.frontend.nodePort }}"
    {{- end }}
    portal.paas/name: {{ .Values.portal.name | quote }}
    portal.paas/description: {{ .Values.portal.description | quote }}
    # 标识这是一个Web应用
    portal.paas/type: "web"
    # 指定访问协议
    portal.paas/protocol: "http"
    {{- end }}
spec:
  type: {{ .Values.services.frontend.type }}
  ports:
    - port: {{ .Values.services.frontend.port }}
      targetPort: {{ .Values.services.frontend.port }}
      {{- if eq .Values.services.frontend.type "NodePort" }}
      nodePort: {{ .Values.services.frontend.nodePort }}
      {{- end }}
      protocol: TCP
      name: http
  selector:
    {{- include "wg-copilot-gpt.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: frontend