# Python API 服务 Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "wg-copilot-gpt.name" . }}-copilot-gpt
  labels:
    {{- include "wg-copilot-gpt.labels" . | nindent 4 }}
    app.kubernetes.io/component: copilot-gpt
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "wg-copilot-gpt.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: copilot-gpt
  template:
    metadata:
      labels:
        {{- include "wg-copilot-gpt.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: copilot-gpt
    spec:
      {{- with .Values.images.pullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      restartPolicy: Always
      containers:
      - name: wg-copilot-gpt
        image: "{{ .Values.images.copilotGpt.repository }}:{{ .Values.images.copilotGpt.tag }}"
        imagePullPolicy: {{ .Values.images.copilotGpt.pullPolicy }}
        command: ["python"]
        args: ["src/main.py", "-H", "0.0.0.0", "-P", "5000"]
        ports:
        - containerPort: {{ .Values.services.copilotGpt.port }}
          protocol: TCP
        env:
        {{- range $key, $value := .Values.env.copilotGpt }}
        - name: {{ $key }}
          value: {{ $value | quote }}
        {{- end }}
        {{- with .Values.resources.copilotGpt }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}

---
# Java 后端服务 Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "wg-copilot-gpt.name" . }}-post-processor
  labels:
    {{- include "wg-copilot-gpt.labels" . | nindent 4 }}
    app.kubernetes.io/component: post-processor
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "wg-copilot-gpt.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: post-processor
  template:
    metadata:
      labels:
        {{- include "wg-copilot-gpt.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: post-processor
    spec:
      {{- with .Values.images.pullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      restartPolicy: Always
      containers:
      - name: post-processor
        image: "{{ .Values.images.postProcessor.repository }}:{{ .Values.images.postProcessor.tag }}"
        imagePullPolicy: {{ .Values.images.postProcessor.pullPolicy }}
        ports:
        - containerPort: {{ .Values.services.postProcessor.port }}
          protocol: TCP
        env:
        {{- range $key, $value := .Values.env.postProcessor }}
        - name: {{ $key }}
          value: {{ $value | quote }}
        {{- end }}
        {{- with .Values.resources.postProcessor }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}

---
# 前端服务 Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "wg-copilot-gpt.name" . }}-frontend
  labels:
    {{- include "wg-copilot-gpt.labels" . | nindent 4 }}
    app.kubernetes.io/component: frontend
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "wg-copilot-gpt.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: frontend
  template:
    metadata:
      labels:
        {{- include "wg-copilot-gpt.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: frontend
    spec:
      {{- with .Values.images.pullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      restartPolicy: Always
      containers:
      - name: frontend
        image: "{{ .Values.images.frontend.repository }}:{{ .Values.images.frontend.tag }}"
        imagePullPolicy: {{ .Values.images.frontend.pullPolicy }}
        ports:
        - containerPort: {{ .Values.services.frontend.port }}
          protocol: TCP
        env:
        - name: API_AGENT_URL
          value: "http://{{ include "wg-copilot-gpt.name" . }}-post-processor-svc:{{ .Values.services.postProcessor.port }}"
        {{- range $key, $value := .Values.env.frontend }}
        - name: {{ $key }}
          value: {{ $value | quote }}
        {{- end }}
        {{- with .Values.resources.frontend }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}