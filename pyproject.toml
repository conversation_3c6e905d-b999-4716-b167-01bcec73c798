[build-system]
requires = ["flit_core==3.9.0"]
build-backend = "flit_core.buildapi"

[project]
name = "wg-copilot-gpt"
version = "1.2.0"
description = "Wireless Guarantee Copilot GPT"
requires-python = ">=3.11, <3.12"
dependencies = [
    "an-copilot==2.5.0rc4",
    "transitions==0.9.2",
    "psycopg2-binary==2.9.9",
    "watchdog>=6.0.0",
    "streamlit>=1.28.0"
]

[tool.flit.module]
name = "src"

[project.optional-dependencies]
lint = [
    "flake8==5.0.4",
    "pyproject-flake8==5.0.4",
    "pre-commit==3.6.0",
    "isort==5.13.1",
    "black==23.12.0"
]
test = [
    "httpx==0.28.1",
    "pytest==7.4.3",
    "coverage==7.2.7",
    "pytest-cov==4.1.0"
]
package = [
    "flit==3.9.0"
]

[tool.flake8]
max-line-length = 300
ignore = ['E203', 'E231', 'E241', 'W291', 'W293', 'W503', 'E501']
per-file-ignores = [
    '__init__.py:F401',
]
count = true

[tool.coverage.report]
exclude_also = [
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
    "except Exception as e:",
]
omit = ["src/main.py", "src/chat.py"]

[tool.uv]
index-url = "https://pypi.tuna.tsinghua.edu.cn/simple"
extra-index-url = [
  "http://nexus.oss.asiainfo.com:8099/nexus/repository/pypi-group/simple"
]
allow-insecure-host = ["pypi.tuna.tsinghua.edu.cn", "nexus.oss.asiainfo.com"]
prerelease = "allow"
